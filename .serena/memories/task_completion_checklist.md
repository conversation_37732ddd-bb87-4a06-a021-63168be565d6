# NS Shop - Task Completion Checklist

## Before Completing Any Task
1. **Type Check**: Run `npx tsc` to ensure no TypeScript errors
2. **Lint Check**: Run `yarn lint` for code quality
3. **Test**: Write and run relevant tests
4. **Documentation**: Update docs if needed (store in `docs/` folder)

## For API Changes
1. Ensure API routes use services instead of Prisma directly
2. Update DTOs and validation schemas if needed
3. Update repositories and services if database changes
4. Test API endpoints manually or with automated tests

## For Database Changes
1. Create Prisma migration: `yarn p:m`
2. Update related repositories, services, models, DTOs
3. Update seeders if needed
4. Test with `yarn db:reset` to ensure clean setup

## For UI Changes
1. Ensure components follow the established patterns
2. Use appropriate UI components from `src/components/ui/`
3. Test responsive design
4. Verify accessibility

## Final Steps
1. Commit changes with descriptive message
2. Ensure development server runs without errors
3. Test critical user flows
4. Document any breaking changes