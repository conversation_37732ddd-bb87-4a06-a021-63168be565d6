# NS Shop - Project Overview

## Purpose
NS Shop is a comprehensive fashion e-commerce platform built with Next.js 15, featuring both customer storefront and admin dashboard functionality.

## Tech Stack
- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS v4, Framer Motion
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js with custom admin auth
- **File Storage**: MinIO for media management
- **Email**: SendGrid/SES providers
- **Testing**: Jest, Playwright
- **Package Manager**: Yarn

## Key Features
- Customer storefront with product catalog, cart, wishlist
- Admin dashboard with comprehensive management tools
- Dependency Injection pattern with services and repositories
- Real-time notifications and WebSocket support
- Audit logging and analytics
- Multi-language support (Vietnamese)
- SEO optimization and dynamic metadata