# NS Shop - Code Style & Conventions

## Architecture Patterns
- **Dependency Injection**: Custom DI container for services and repositories
- **Repository Pattern**: Data access layer with base repository
- **Service Layer**: Business logic separation from API routes
- **DTO Pattern**: Zod validation for request/response objects

## File Organization
- `src/app/api/` - API routes (should use services, not Prisma directly)
- `src/app/api/services/` - Business logic services
- `src/app/api/repositories/` - Data access repositories
- `src/app/models/` - Data models and entities
- `src/app/dto/` - Data Transfer Objects with Zod validation
- `src/components/` - React components (admin/, shop/, ui/)

## Naming Conventions
- **Files**: kebab-case (e.g., `user-service.ts`)
- **Classes**: PascalCase (e.g., `UserService`)
- **Functions**: camelCase (e.g., `getUserById`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `USER_SERVICE`)

## TypeScript Guidelines
- Strict mode enabled
- Always use explicit types for function parameters and returns
- Use interfaces for object shapes
- Use enums for fixed sets of values
- Prefer `const` assertions for immutable data

## Import Organization
1. Node.js built-ins
2. External libraries
3. Internal utilities and services
4. Relative imports