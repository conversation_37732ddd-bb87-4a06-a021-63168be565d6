# NS Shop - Suggested Commands

## Development Commands
- `yarn dev` - Start development server on port 6002
- `yarn build` - Build for production (includes Prisma migration)
- `yarn start` - Start production server
- `yarn lint` - Run TypeScript type checking

## Database Commands
- `yarn p:m` - Run Prisma migrations in development
- `yarn p:m:r` - Reset Prisma migrations
- `yarn p:s` - Open Prisma Studio
- `yarn db:seed` - Seed database with test data
- `yarn db:reset` - Reset database and reseed

## Testing Commands
- `yarn test` - Run Jest tests

## Docker Commands
- `yarn dup` - Start Docker Compose services

## System Commands (macOS)
- `ls` - List directory contents
- `find . -name "*.ts" -type f` - Find TypeScript files
- `grep -r "pattern" src/` - Search for patterns in source code
- `git status` - Check Git status
- `git log --oneline` - View commit history

## Development Server
- Local development: http://localhost:6002
- Admin panel: http://localhost:6002/admin