# NS Shop - Dependency Injection Architecture

## DI Container Structure
- **Container**: `src/app/api/di-container.ts` - Custom DI implementation
- **Setup**: `src/app/api/di-setup.ts` - Service and repository registration
- **Identifiers**: `src/app/api/services/service-identifiers.ts` - Service constants

## Service Layer
- **Base Service**: `src/app/api/services/base.service.ts`
- **Services**: Individual services for each domain (user, product, order, etc.)
- **Pattern**: Services inject repositories and contain business logic

## Repository Layer
- **Base Repository**: `src/app/api/repositories/base.repository.ts`
- **Repositories**: Data access layer with Prisma integration
- **Factory**: `src/app/api/repositories/repository.factory.ts`

## API Route Pattern
```typescript
// CORRECT: Use DI container to resolve services
import { container } from '@/app/api/di-container';
import { USER_SERVICE } from '@/app/api/services/service-identifiers';

export async function GET() {
  const userService = container.resolve(USER_SERVICE);
  return userService.getUsers();
}

// INCORRECT: Direct Prisma usage
import { prisma } from '@/lib/prisma';
export async function GET() {
  return prisma.user.findMany();
}
```

## Initialization
- DI container is initialized in `di-setup.ts`
- Services and repositories are registered with their identifiers
- Container resolves dependencies automatically