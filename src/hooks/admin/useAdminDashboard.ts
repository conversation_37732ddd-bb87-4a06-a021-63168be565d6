import { useCallback, useEffect, useState } from 'react';
import { useAdminApi } from './useAdminApi';

export interface DashboardStatsDto {
  totalUsers: number;
  totalProducts: number;
  totalOrders: number;
  totalRevenue: number;
  recentUsers: number;
  recentOrders: number;
  recentRevenue: number;
  conversionRate: number;
  averageOrderValue: number;
  topSellingProducts: Array<{
    id: string;
    name: string;
    sales: number;
    revenue: number;
  }>;
  recentOrders: Array<{
    id: string;
    customerName: string;
    total: number;
    status: string;
    createdAt: string;
  }>;
  salesChart: Array<{
    date: string;
    sales: number;
    revenue: number;
  }>;
  userRegistrations: Array<{
    date: string;
    count: number;
  }>;
}

export interface DashboardOverviewDto {
  totalUsers: number;
  totalProducts: number;
  totalOrders: number;
  totalRevenue: number;
  growthMetrics: {
    usersGrowth: number;
    ordersGrowth: number;
    revenueGrowth: number;
    productsGrowth: number;
  };
}

export interface DashboardAlertDto {
  id: string;
  type: 'LOW_STOCK' | 'HIGH_REFUND_RATE' | 'PAYMENT_FAILED' | 'SYSTEM_ERROR';
  title: string;
  message: string;
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  createdAt: string;
  resolved: boolean;
  metadata?: Record<string, any>;
}

export interface PerformanceMetricsDto {
  apiResponseTime: number;
  databaseQueryTime: number;
  cacheHitRate: number;
  errorRate: number;
  activeUsers: number;
  serverUptime: number;
  memoryUsage: number;
  cpuUsage: number;
}

export function useAdminDashboard() {
  const [stats, setStats] = useState<DashboardStatsDto | null>(null);
  const [overview, setOverview] = useState<DashboardOverviewDto | null>(null);
  const [alerts, setAlerts] = useState<DashboardAlertDto[]>([]);
  const [performance, setPerformance] = useState<PerformanceMetricsDto | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { apiCall, isOperationLoading } = useAdminApi();

  const fetchDashboardStats = useCallback(async (period: '7d' | '30d' | '90d' = '30d') => {
    try {
      const response = await apiCall<DashboardStatsDto>(
        `/api/admin/dashboard?period=${period}`,
        { method: 'GET' },
        'fetch-dashboard-stats'
      );

      if (response.success && response.data) {
        setStats(response.data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch dashboard stats');
    }
  }, [apiCall]);

  const fetchDashboardOverview = useCallback(async () => {
    try {
      const response = await apiCall<DashboardOverviewDto>(
        '/api/admin/dashboard/overview',
        { method: 'GET' },
        'fetch-dashboard-overview'
      );

      if (response.success && response.data) {
        setOverview(response.data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch dashboard overview');
    }
  }, [apiCall]);

  const fetchDashboardAlerts = useCallback(async () => {
    try {
      const response = await apiCall<DashboardAlertDto[]>(
        '/api/admin/dashboard/alerts',
        { method: 'GET' },
        'fetch-dashboard-alerts'
      );

      if (response.success && response.data) {
        setAlerts(response.data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch dashboard alerts');
    }
  }, [apiCall]);

  const fetchPerformanceMetrics = useCallback(async () => {
    try {
      const response = await apiCall<PerformanceMetricsDto>(
        '/api/admin/performance',
        { method: 'GET' },
        'fetch-performance-metrics'
      );

      if (response.success && response.data) {
        setPerformance(response.data);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch performance metrics');
    }
  }, [apiCall]);

  const resolveAlert = useCallback(async (alertId: string): Promise<boolean> => {
    try {
      const response = await apiCall(
        `/api/admin/dashboard/alerts/${alertId}`,
        {
          method: 'PUT',
          body: { resolved: true },
        },
        `resolve-alert-${alertId}`
      );

      if (response.success) {
        setAlerts(prev => prev.map(alert => 
          alert.id === alertId ? { ...alert, resolved: true } : alert
        ));
        return true;
      }
      return false;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to resolve alert');
    }
  }, [apiCall]);

  const dismissAlert = useCallback(async (alertId: string): Promise<boolean> => {
    try {
      const response = await apiCall(
        `/api/admin/dashboard/alerts/${alertId}`,
        { method: 'DELETE' },
        `dismiss-alert-${alertId}`
      );

      if (response.success) {
        setAlerts(prev => prev.filter(alert => alert.id !== alertId));
        return true;
      }
      return false;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to dismiss alert');
    }
  }, [apiCall]);

  const refreshAllData = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      await Promise.all([
        fetchDashboardStats(),
        fetchDashboardOverview(),
        fetchDashboardAlerts(),
        fetchPerformanceMetrics(),
      ]);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to refresh dashboard data');
    } finally {
      setLoading(false);
    }
  }, [fetchDashboardStats, fetchDashboardOverview, fetchDashboardAlerts, fetchPerformanceMetrics]);

  // Auto-fetch on mount
  useEffect(() => {
    if (!stats && !overview && !loading) {
      refreshAllData();
    }
  }, [stats, overview, loading, refreshAllData]);

  // Helper functions
  const getUnresolvedAlerts = useCallback(() => {
    return alerts.filter(alert => !alert.resolved);
  }, [alerts]);

  const getCriticalAlerts = useCallback(() => {
    return alerts.filter(alert => !alert.resolved && alert.severity === 'CRITICAL');
  }, [alerts]);

  const getAlertsByType = useCallback((type: DashboardAlertDto['type']) => {
    return alerts.filter(alert => alert.type === type);
  }, [alerts]);

  return {
    // Data
    stats,
    overview,
    alerts,
    performance,
    loading,
    error,

    // Actions
    fetchDashboardStats,
    fetchDashboardOverview,
    fetchDashboardAlerts,
    fetchPerformanceMetrics,
    resolveAlert,
    dismissAlert,
    refreshAllData,

    // Helper functions
    getUnresolvedAlerts,
    getCriticalAlerts,
    getAlertsByType,

    // Loading states
    isFetchingStats: isOperationLoading('fetch-dashboard-stats'),
    isFetchingOverview: isOperationLoading('fetch-dashboard-overview'),
    isFetchingAlerts: isOperationLoading('fetch-dashboard-alerts'),
    isFetchingPerformance: isOperationLoading('fetch-performance-metrics'),
    isResolvingAlert: (id: string) => isOperationLoading(`resolve-alert-${id}`),
    isDismissingAlert: (id: string) => isOperationLoading(`dismiss-alert-${id}`),
  };
}