import { useCallback, useEffect } from 'react';
import { useAdminState } from '@/contexts/AdminContext';
import { useAdminApi } from './useAdminApi';
import { ProductDto, CreateProductDto, UpdateProductDto } from '@/app/dto';

export interface AdminProductsFilters {
  search?: string;
  category?: string;
  brand?: string;
  status?: 'ACTIVE' | 'INACTIVE' | 'DRAFT';
  featured?: boolean;
}

export function useAdminProducts() {
  const {
    data: products,
    loading,
    error,
    pagination,
    filters,
    updateData,
    updatePagination,
    updateFilters,
    setLoading,
    setError,
    refresh,
  } = useAdminState<ProductDto>('admin-products');

  const { apiCall, isOperationLoading } = useAdminApi();

  const fetchProducts = useCallback(async (
    page: number = 1,
    limit: number = 20,
    searchFilters: AdminProductsFilters = {}
  ) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...Object.fromEntries(
          Object.entries(searchFilters).filter(([_, v]) => v !== undefined && v !== '')
        ),
      });

      const response = await apiCall<ProductDto[]>(
        `/api/admin/products?${params}`,
        { method: 'GET' }
      );

      if (response.success && response.data) {
        updateData(response.data);
        if (response.pagination) {
          updatePagination(response.pagination);
        }
        updateFilters(searchFilters);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch products');
    }
  }, [apiCall, updateData, updatePagination, updateFilters, setLoading, setError]);

  const createProduct = useCallback(async (productData: CreateProductDto): Promise<ProductDto | null> => {
    try {
      const response = await apiCall<ProductDto>(
        '/api/admin/products',
        {
          method: 'POST',
          body: productData,
        },
        'create-product'
      );

      if (response.success && response.data) {
        refresh();
        return response.data;
      }
      return null;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to create product');
    }
  }, [apiCall, refresh]);

  const updateProduct = useCallback(async (
    id: string,
    productData: UpdateProductDto
  ): Promise<ProductDto | null> => {
    try {
      const response = await apiCall<ProductDto>(
        `/api/admin/products/${id}`,
        {
          method: 'PUT',
          body: productData,
        },
        `update-product-${id}`
      );

      if (response.success && response.data) {
        const updatedProducts = products.map(product => 
          product.id === id ? response.data! : product
        );
        updateData(updatedProducts);
        return response.data;
      }
      return null;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to update product');
    }
  }, [apiCall, products, updateData]);

  const deleteProduct = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await apiCall(
        `/api/admin/products/${id}`,
        { method: 'DELETE' },
        `delete-product-${id}`
      );

      if (response.success) {
        const filteredProducts = products.filter(product => product.id !== id);
        updateData(filteredProducts);
        return true;
      }
      return false;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to delete product');
    }
  }, [apiCall, products, updateData]);

  const getProductById = useCallback(async (id: string): Promise<ProductDto | null> => {
    try {
      const response = await apiCall<ProductDto>(
        `/api/admin/products/${id}`,
        { method: 'GET' },
        `get-product-${id}`
      );

      return response.success && response.data ? response.data : null;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to get product');
    }
  }, [apiCall]);

  const bulkUpdateProducts = useCallback(async (
    ids: string[],
    updates: Partial<ProductDto>
  ): Promise<boolean> => {
    try {
      const response = await apiCall(
        '/api/admin/products/bulk',
        {
          method: 'PUT',
          body: { ids, updates },
        },
        'bulk-update-products'
      );

      if (response.success) {
        refresh();
        return true;
      }
      return false;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to bulk update products');
    }
  }, [apiCall, refresh]);

  const uploadProductMedia = useCallback(async (
    productId: string,
    mediaFiles: FormData
  ): Promise<boolean> => {
    try {
      const response = await fetch(`/api/admin/products/${productId}/media`, {
        method: 'POST',
        body: mediaFiles,
        credentials: 'include',
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // Refresh product data to get updated media
        const updatedProduct = await getProductById(productId);
        if (updatedProduct) {
          const updatedProducts = products.map(product => 
            product.id === productId ? updatedProduct : product
          );
          updateData(updatedProducts);
        }
        return true;
      }
      return false;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to upload media');
    }
  }, [getProductById, products, updateData]);

  const searchProducts = useCallback((searchFilters: AdminProductsFilters) => {
    fetchProducts(1, pagination.limit, searchFilters);
  }, [fetchProducts, pagination.limit]);

  const changePage = useCallback((page: number) => {
    fetchProducts(page, pagination.limit, filters);
  }, [fetchProducts, pagination.limit, filters]);

  const changePageSize = useCallback((limit: number) => {
    fetchProducts(1, limit, filters);
  }, [fetchProducts, filters]);

  // Auto-fetch on mount if no data
  useEffect(() => {
    if (products.length === 0 && !loading && !error) {
      fetchProducts();
    }
  }, [products.length, loading, error, fetchProducts]);

  return {
    // Data
    products,
    loading,
    error,
    pagination,
    filters,

    // Actions
    fetchProducts,
    createProduct,
    updateProduct,
    deleteProduct,
    getProductById,
    bulkUpdateProducts,
    uploadProductMedia,
    searchProducts,
    changePage,
    changePageSize,
    refresh,

    // Loading states
    isCreating: isOperationLoading('create-product'),
    isUpdating: (id: string) => isOperationLoading(`update-product-${id}`),
    isDeleting: (id: string) => isOperationLoading(`delete-product-${id}`),
    isGettingProduct: (id: string) => isOperationLoading(`get-product-${id}`),
    isBulkUpdating: isOperationLoading('bulk-update-products'),
  };
}