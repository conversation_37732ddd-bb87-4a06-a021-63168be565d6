import { useCallback, useEffect } from 'react';
import { useAdminState } from '@/contexts/AdminContext';
import { useAdminApi } from './useAdminApi';
import { UserDto, CreateUserDto, UpdateUserDto } from '@/app/dto';

export interface AdminUsersFilters {
  search?: string;
  role?: string;
  status?: string;
}

export function useAdminUsers() {
  const {
    data: users,
    loading,
    error,
    pagination,
    filters,
    updateData,
    updatePagination,
    updateFilters,
    setLoading,
    setError,
    refresh,
  } = useAdminState<UserDto>('admin-users');

  const { apiCall, isOperationLoading } = useAdminApi();

  const fetchUsers = useCallback(async (
    page: number = 1,
    limit: number = 20,
    searchFilters: AdminUsersFilters = {}
  ) => {
    setLoading(true);
    setError(null);

    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...Object.fromEntries(
          Object.entries(searchFilters).filter(([_, v]) => v !== undefined && v !== '')
        ),
      });

      const response = await apiCall<UserDto[]>(
        `/api/admin/users?${params}`,
        { method: 'GET' }
      );

      if (response.success && response.data) {
        updateData(response.data);
        if (response.pagination) {
          updatePagination(response.pagination);
        }
        updateFilters(searchFilters);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch users');
    }
  }, [apiCall, updateData, updatePagination, updateFilters, setLoading, setError]);

  const createUser = useCallback(async (userData: CreateUserDto): Promise<UserDto | null> => {
    try {
      const response = await apiCall<UserDto>(
        '/api/admin/users',
        {
          method: 'POST',
          body: userData,
        },
        'create-user'
      );

      if (response.success && response.data) {
        // Refresh the list after creation
        refresh();
        return response.data;
      }
      return null;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to create user');
    }
  }, [apiCall, refresh]);

  const updateUser = useCallback(async (
    id: string,
    userData: UpdateUserDto
  ): Promise<UserDto | null> => {
    try {
      const response = await apiCall<UserDto>(
        `/api/admin/users/${id}`,
        {
          method: 'PUT',
          body: userData,
        },
        `update-user-${id}`
      );

      if (response.success && response.data) {
        // Update the user in the current list
        const updatedUsers = users.map(user => 
          user.id === id ? response.data! : user
        );
        updateData(updatedUsers);
        return response.data;
      }
      return null;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to update user');
    }
  }, [apiCall, users, updateData]);

  const deleteUser = useCallback(async (id: string): Promise<boolean> => {
    try {
      const response = await apiCall(
        `/api/admin/users/${id}`,
        { method: 'DELETE' },
        `delete-user-${id}`
      );

      if (response.success) {
        // Remove the user from the current list
        const filteredUsers = users.filter(user => user.id !== id);
        updateData(filteredUsers);
        return true;
      }
      return false;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to delete user');
    }
  }, [apiCall, users, updateData]);

  const getUserById = useCallback(async (id: string): Promise<UserDto | null> => {
    try {
      const response = await apiCall<UserDto>(
        `/api/admin/users/${id}`,
        { method: 'GET' },
        `get-user-${id}`
      );

      return response.success && response.data ? response.data : null;
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Failed to get user');
    }
  }, [apiCall]);

  const searchUsers = useCallback((searchFilters: AdminUsersFilters) => {
    fetchUsers(1, pagination.limit, searchFilters);
  }, [fetchUsers, pagination.limit]);

  const changePage = useCallback((page: number) => {
    fetchUsers(page, pagination.limit, filters);
  }, [fetchUsers, pagination.limit, filters]);

  const changePageSize = useCallback((limit: number) => {
    fetchUsers(1, limit, filters);
  }, [fetchUsers, filters]);

  // Auto-fetch on mount if no data
  useEffect(() => {
    if (users.length === 0 && !loading && !error) {
      fetchUsers();
    }
  }, [users.length, loading, error, fetchUsers]);

  return {
    // Data
    users,
    loading,
    error,
    pagination,
    filters,

    // Actions
    fetchUsers,
    createUser,
    updateUser,
    deleteUser,
    getUserById,
    searchUsers,
    changePage,
    changePageSize,
    refresh,

    // Loading states
    isCreating: isOperationLoading('create-user'),
    isUpdating: (id: string) => isOperationLoading(`update-user-${id}`),
    isDeleting: (id: string) => isOperationLoading(`delete-user-${id}`),
    isGettingUser: (id: string) => isOperationLoading(`get-user-${id}`),
  };
}