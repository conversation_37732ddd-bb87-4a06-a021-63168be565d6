/**
 * User Transform Functions
 * Chuyển đổi giữa User entities và DTOs
 */

import {
  UserEntity,
  UserWithRelations,
  CreateUserData,
  UpdateUserData,
  UserStats,
  UserPreferences,
  UserProfile,
} from "../../models/user.model";
import {
  UserResponseDto,
  UserWithRelationsDto,
  CreateUserRequestDto,
  UpdateUserRequestDto,
  UserAuthResponseDto,
  UserStatsDto,
  UserPreferencesDto,
  UserProfileDto,
} from "../user.dto";
import { UserRole } from "../../models/common.model";
import { UserRoleDto } from "../common.dto";
import {
  toISOString,
  fromISOString,
  removeUndefined,
} from "./common.transform";

/**
 * Transform UserEntity to UserResponseDto
 */
export function userToResponseDto(user: UserEntity): UserResponseDto {
  return removeUndefined({
    id: user.id,
    email: user.email,
    name: user.name,
    avatar: user.avatar,
    phone: user.phone,
    dateOfBirth: toISOString(user.dateOfBirth),
    gender: user.gender,
    isActive: user.isActive,
    role: transformUserRole(user.role),
    emailVerified: toISOString(user.emailVerified),
    phoneVerified: toISOString(user.phoneVerified),
    lastLoginAt: toISOString(user.lastLoginAt),
    createdAt: toISOString(user.createdAt)!,
    updatedAt: toISOString(user.updatedAt)!,
    preferences: user.preferences
      ? userPreferencesToDto(user.preferences)
      : undefined,
    profile: user.profile ? userProfileToDto(user.profile) : undefined,
  }) as UserResponseDto;
}

/**
 * Transform UserWithRelations to UserWithRelationsDto
 */
export function userWithRelationsToDto(
  user: UserWithRelations
): UserWithRelationsDto {
  return {
    ...userToResponseDto(user),
    stats: user.stats ? userStatsToDto(user.stats) : undefined,
    orderCount: user.orders?.length,
    reviewCount: user.reviews?.length,
    addressCount: user.addresses?.length,
  };
}

/**
 * Transform UserEntity to UserAuthResponseDto
 */
export function userToAuthResponseDto(user: UserEntity): UserAuthResponseDto {
  return {
    id: user.id,
    email: user.email,
    name: user.name,
    role: transformUserRole(user.role),
    avatar: user.avatar,
    isActive: user.isActive,
    emailVerified: toISOString(user.emailVerified),
  };
}

/**
 * Transform CreateUserRequestDto to CreateUserData
 */
export function createUserRequestToData(
  dto: CreateUserRequestDto
): CreateUserData {
  return removeUndefined({
    email: dto.email,
    name: dto.name,
    password: dto.password,
    phone: dto.phone,
    dateOfBirth: fromISOString(dto.dateOfBirth),
    gender: dto.gender,
    role: transformUserRoleFromDto(dto.role),
  }) as CreateUserData;
}

/**
 * Transform UpdateUserRequestDto to UpdateUserData
 */
export function updateUserRequestToData(
  dto: UpdateUserRequestDto
): UpdateUserData {
  return removeUndefined({
    name: dto.name,
    phone: dto.phone,
    dateOfBirth: fromISOString(dto.dateOfBirth),
    gender: dto.gender,
    avatar: dto.avatar,
    preferences: dto.preferences
      ? userPreferencesFromDto(dto.preferences)
      : undefined,
    profile: dto.profile ? userProfileFromDto(dto.profile) : undefined,
  }) as UpdateUserData;
}

/**
 * Transform UserStats to UserStatsDto
 */
export function userStatsToDto(stats: UserStats): UserStatsDto {
  return {
    totalOrders: stats.totalOrders,
    totalSpent: stats.totalSpent,
    totalReviews: stats.totalReviews,
    averageRating: stats.averageRating,
    joinedDays: stats.joinedDays,
    lastOrderDate: toISOString(stats.lastOrderDate),
  };
}

/**
 * Transform UserPreferences to UserPreferencesDto
 */
export function userPreferencesToDto(
  preferences: UserPreferences
): UserPreferencesDto {
  return {
    language: preferences.language,
    currency: preferences.currency,
    timezone: preferences.timezone,
    notifications: {
      email: preferences.notifications.email,
      sms: preferences.notifications.sms,
      push: preferences.notifications.push,
      marketing: preferences.notifications.marketing,
      orderUpdates: preferences.notifications.orderUpdates,
      promotions: preferences.notifications.promotions,
    },
    privacy: {
      profileVisibility: preferences.privacy.profileVisibility,
      showEmail: preferences.privacy.showEmail,
      showPhone: preferences.privacy.showPhone,
      allowDataCollection: preferences.privacy.allowDataCollection,
    },
  };
}

/**
 * Transform UserPreferencesDto to UserPreferences
 */
export function userPreferencesFromDto(dto: any): Partial<UserPreferences> {
  return removeUndefined({
    language: dto.language,
    currency: dto.currency,
    timezone: dto.timezone,
    notifications: dto.notifications
      ? {
          email: dto.notifications.email ?? true,
          sms: dto.notifications.sms ?? false,
          push: dto.notifications.push ?? true,
          marketing: dto.notifications.marketing ?? false,
          orderUpdates: dto.notifications.orderUpdates ?? true,
          promotions: dto.notifications.promotions ?? false,
        }
      : undefined,
    privacy: dto.privacy
      ? {
          profileVisibility: dto.privacy.profileVisibility ?? "PUBLIC",
          showEmail: dto.privacy.showEmail ?? false,
          showPhone: dto.privacy.showPhone ?? false,
          allowDataCollection: dto.privacy.allowDataCollection ?? true,
        }
      : undefined,
  });
}

/**
 * Transform UserProfile to UserProfileDto
 */
export function userProfileToDto(profile: UserProfile): UserProfileDto {
  return removeUndefined({
    bio: profile.bio,
    website: profile.website,
    location: profile.location,
    interests: profile.interests,
    socialMedia: profile.socialMedia,
  }) as UserProfileDto;
}

/**
 * Transform UserProfileDto to UserProfile
 */
export function userProfileFromDto(
  dto: Partial<UserProfileDto>
): Partial<UserProfile> {
  return removeUndefined({
    bio: dto.bio,
    website: dto.website,
    location: dto.location,
    interests: dto.interests,
    socialMedia: dto.socialMedia,
  });
}

/**
 * Transform UserRole to UserRoleDto
 */
export function transformUserRole(role: UserRole): UserRoleDto {
  switch (role) {
    case UserRole.USER:
      return UserRoleDto.USER;
    case UserRole.ADMIN:
      return UserRoleDto.ADMIN;
    case UserRole.SUPER_ADMIN:
      return UserRoleDto.SUPER_ADMIN;
    default:
      return UserRoleDto.USER;
  }
}

/**
 * Transform UserRoleDto to UserRole
 */
export function transformUserRoleFromDto(role?: UserRoleDto): UserRole {
  switch (role) {
    case UserRoleDto.USER:
      return UserRole.USER;
    case UserRoleDto.ADMIN:
      return UserRole.ADMIN;
    case UserRoleDto.SUPER_ADMIN:
      return UserRole.SUPER_ADMIN;
    default:
      return UserRole.USER;
  }
}

/**
 * Transform array of users to DTOs
 */
export function usersToResponseDtos(users: UserEntity[]): UserResponseDto[] {
  return users.map(userToResponseDto);
}

/**
 * Transform array of users with relations to DTOs
 */
export function usersWithRelationsToDtos(
  users: UserWithRelations[]
): UserWithRelationsDto[] {
  return users.map(userWithRelationsToDto);
}

/**
 * Sanitize user data (remove sensitive fields)
 */
export function sanitizeUserData(
  user: UserEntity
): Omit<UserEntity, "password"> {
  const { password: _password, ...sanitized } = user;
  return sanitized;
}

/**
 * Transform user for public display (minimal data)
 */
export function userToPublicDto(user: UserEntity): {
  id: string;
  name: string;
  avatar?: string;
} {
  return {
    id: user.id,
    name: user.name,
    avatar: user.avatar,
  };
}

/**
 * Transform user search filters
 */
export function transformUserSearchFilters(params: Record<string, any>) {
  return removeUndefined({
    search: params.search,
    isActive:
      params.isActive !== undefined ? Boolean(params.isActive) : undefined,
    role: params.role ? transformUserRoleFromDto(params.role) : undefined,
    gender: params.gender,
    dateFrom: fromISOString(params.dateFrom),
    dateTo: fromISOString(params.dateTo),
    hasOrders:
      params.hasOrders !== undefined ? Boolean(params.hasOrders) : undefined,
    hasReviews:
      params.hasReviews !== undefined ? Boolean(params.hasReviews) : undefined,
  });
}
