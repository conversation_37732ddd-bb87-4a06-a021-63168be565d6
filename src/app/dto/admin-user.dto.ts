/**
 * Admin User DTOs
 * Data Transfer Objects cho Admin User
 */

import { z } from 'zod';
import { UserRoleDto } from './common.dto';
import { EmailSchema, PasswordSchema } from './common.dto';

/**
 * Admin User Response DTO
 */
export interface AdminUserResponseDto {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: UserRoleDto;
  isActive: boolean;
  permissions: string[];
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  metadata?: Record<string, any>;
}

/**
 * Create Admin User Request DTO
 */
export interface CreateAdminUserRequestDto {
  email: string;
  name: string;
  password: string;
  role: UserRoleDto;
  permissions?: string[];
  avatar?: string;
}

/**
 * Update Admin User Request DTO
 */
export interface UpdateAdminUserRequestDto {
  name?: string;
  avatar?: string;
  role?: UserRoleDto;
  permissions?: string[];
  isActive?: boolean;
}

/**
 * Validation Schemas
 */
export const CreateAdminUserRequestSchema = z.object({
  email: EmailSchema,
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name too long'),
  password: PasswordSchema,
  role: z.nativeEnum(UserRoleDto),
  permissions: z.array(z.string()).optional(),
  avatar: z.string().url().optional(),
});

export const UpdateAdminUserRequestSchema = z.object({
  name: z.string().min(2, 'Name must be at least 2 characters').max(100, 'Name too long').optional(),
  avatar: z.string().url().optional(),
  role: z.nativeEnum(UserRoleDto).optional(),
  permissions: z.array(z.string()).optional(),
  isActive: z.boolean().optional(),
});

/**
 * Validation functions
 */
export const validateCreateAdminUser = (data: any): CreateAdminUserRequestDto => {
  return CreateAdminUserRequestSchema.parse(data);
};

export const validateUpdateAdminUser = (data: any): UpdateAdminUserRequestDto => {
  return UpdateAdminUserRequestSchema.parse(data);
};
