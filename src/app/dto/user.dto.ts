/**
 * User DTOs
 * Data Transfer Objects cho User
 */

import { z } from "zod";
import { UserRoleDto } from "./common.dto";
import { EmailSchema, PasswordSchema, PhoneSchema } from "./common.dto";

/**
 * User Response DTO
 */
export interface UserResponseDto {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: string;
  isActive: boolean;
  role: UserRoleDto;
  emailVerified?: string;
  phoneVerified?: string;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
  preferences?: UserPreferencesDto;
  profile?: UserProfileDto;
}

/**
 * User Preferences DTO
 */
export interface UserPreferencesDto {
  language: string;
  currency: string;
  timezone: string;
  notifications: NotificationPreferencesDto;
  privacy: PrivacySettingsDto;
}

export interface NotificationPreferencesDto {
  email: boolean;
  sms: boolean;
  push: boolean;
  marketing: boolean;
  orderUpdates: boolean;
  promotions: boolean;
}

export interface PrivacySettingsDto {
  profileVisibility: "PUBLIC" | "PRIVATE" | "FRIENDS";
  showEmail: boolean;
  showPhone: boolean;
  allowDataCollection: boolean;
}

/**
 * User Profile DTO
 */
export interface UserProfileDto {
  bio?: string;
  website?: string;
  location?: string;
  interests?: string[];
  socialMedia?: Record<string, string>;
}

/**
 * User with Relations DTO
 */
export interface UserWithRelationsDto extends UserResponseDto {
  stats?: UserStatsDto;
  orderCount?: number;
  reviewCount?: number;
  addressCount?: number;
}

/**
 * User Statistics DTO
 */
export interface UserStatsDto {
  totalOrders: number;
  totalSpent: number;
  totalReviews: number;
  averageRating: number;
  joinedDays: number;
  lastOrderDate?: string;
}

/**
 * Create User Request DTO
 */
export interface CreateUserRequestDto {
  email: string;
  name: string;
  password: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: string;
  role?: UserRoleDto;
}

/**
 * Update User Request DTO
 */
export interface UpdateUserRequestDto {
  name?: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: "MALE" | "FEMALE" | "OTHER";
  avatar?: string;
  preferences?: {
    language?: string;
    currency?: string;
    timezone?: string;
    notifications?: {
      email?: boolean;
      sms?: boolean;
      push?: boolean;
      marketing?: boolean;
      orderUpdates?: boolean;
      promotions?: boolean;
    };
    privacy?: {
      profileVisibility?: "PUBLIC" | "PRIVATE" | "FRIENDS";
      showEmail?: boolean;
      showPhone?: boolean;
      allowDataCollection?: boolean;
    };
  };
  profile?: {
    bio?: string;
    website?: string;
    location?: string;
    interests?: string[];
    socialMedia?: Record<string, string>;
  };
}

/**
 * Update User Password Request DTO
 */
export interface UpdateUserPasswordRequestDto {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

/**
 * User Search Request DTO
 */
export interface UserSearchRequestDto {
  search?: string;
  isActive?: boolean;
  role?: UserRoleDto;
  gender?: string;
  dateFrom?: string;
  dateTo?: string;
  hasOrders?: boolean;
  hasReviews?: boolean;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: "asc" | "desc";
}

/**
 * User Authentication DTOs
 */
export interface UserAuthResponseDto {
  id: string;
  email: string;
  name: string;
  role: UserRoleDto;
  avatar?: string;
  isActive: boolean;
  emailVerified?: string;
}

export interface LoginRequestDto {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface LoginResponseDto {
  user: UserAuthResponseDto;
  token?: string;
  refreshToken?: string;
  expiresAt?: string;
}

export interface RegisterRequestDto {
  email: string;
  name: string;
  password: string;
  confirmPassword: string;
  phone?: string;
  acceptTerms: boolean;
}

export interface ForgotPasswordRequestDto {
  email: string;
}

export interface ResetPasswordRequestDto {
  token: string;
  password: string;
  confirmPassword: string;
}

export interface VerifyEmailRequestDto {
  token: string;
}

/**
 * Validation Schemas
 */
export const CreateUserRequestSchema = z.object({
  email: EmailSchema,
  name: z
    .string()
    .min(2, "Name must be at least 2 characters")
    .max(100, "Name too long"),
  password: PasswordSchema,
  phone: PhoneSchema.optional(),
  dateOfBirth: z.string().datetime().optional(),
  gender: z.enum(["MALE", "FEMALE", "OTHER"]).optional(),
  role: z.nativeEnum(UserRoleDto).default(UserRoleDto.USER),
});

export const UpdateUserRequestSchema = z.object({
  name: z
    .string()
    .min(2, "Name must be at least 2 characters")
    .max(100, "Name too long")
    .optional(),
  phone: PhoneSchema.optional(),
  dateOfBirth: z.string().datetime().optional(),
  gender: z.enum(["MALE", "FEMALE", "OTHER"]).optional(),
  avatar: z.string().url().optional(),
  preferences: z
    .object({
      language: z.string().optional(),
      currency: z.string().optional(),
      timezone: z.string().optional(),
      notifications: z
        .object({
          email: z.boolean().optional(),
          sms: z.boolean().optional(),
          push: z.boolean().optional(),
          marketing: z.boolean().optional(),
          orderUpdates: z.boolean().optional(),
          promotions: z.boolean().optional(),
        })
        .optional(),
      privacy: z
        .object({
          profileVisibility: z
            .enum(["PUBLIC", "PRIVATE", "FRIENDS"])
            .optional(),
          showEmail: z.boolean().optional(),
          showPhone: z.boolean().optional(),
          allowDataCollection: z.boolean().optional(),
        })
        .optional(),
    })
    .optional(),
  profile: z
    .object({
      bio: z.string().max(500).optional(),
      website: z.string().url().optional(),
      location: z.string().max(100).optional(),
      interests: z.array(z.string()).optional(),
      socialMedia: z.record(z.string()).optional(),
    })
    .optional(),
});

export const UpdateUserPasswordRequestSchema = z
  .object({
    currentPassword: z.string().min(1, "Current password is required"),
    newPassword: PasswordSchema,
    confirmPassword: z.string().min(1, "Confirm password is required"),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

export const UserSearchRequestSchema = z.object({
  search: z.string().optional(),
  isActive: z.coerce.boolean().optional(),
  role: z.nativeEnum(UserRoleDto).optional(),
  gender: z.enum(["MALE", "FEMALE", "OTHER"]).optional(),
  dateFrom: z.string().datetime().optional(),
  dateTo: z.string().datetime().optional(),
  hasOrders: z.coerce.boolean().optional(),
  hasReviews: z.coerce.boolean().optional(),
  page: z.coerce.number().int().min(1).default(1),
  limit: z.coerce.number().int().min(1).max(100).default(10),
  sortBy: z.string().default("createdAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
});

export const LoginRequestSchema = z.object({
  email: EmailSchema,
  password: z.string().min(1, "Password is required"),
  rememberMe: z.boolean().default(false),
});

export const RegisterRequestSchema = z
  .object({
    email: EmailSchema,
    name: z
      .string()
      .min(2, "Name must be at least 2 characters")
      .max(100, "Name too long"),
    password: PasswordSchema,
    confirmPassword: z.string().min(1, "Confirm password is required"),
    phone: PhoneSchema.optional(),
    acceptTerms: z.boolean().refine((val) => val === true, {
      message: "You must accept the terms and conditions",
    }),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

export const ForgotPasswordRequestSchema = z.object({
  email: EmailSchema,
});

export const ResetPasswordRequestSchema = z
  .object({
    token: z.string().min(1, "Token is required"),
    password: PasswordSchema,
    confirmPassword: z.string().min(1, "Confirm password is required"),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords don't match",
    path: ["confirmPassword"],
  });

export const VerifyEmailRequestSchema = z.object({
  token: z.string().min(1, "Token is required"),
});

/**
 * Validation functions
 */
export const validateCreateUser = (data: any): CreateUserRequestDto => {
  return CreateUserRequestSchema.parse(data);
};

export const validateUpdateUser = (data: any): UpdateUserRequestDto => {
  return UpdateUserRequestSchema.parse(data);
};

export const validateUpdateUserPassword = (
  data: any
): UpdateUserPasswordRequestDto => {
  return UpdateUserPasswordRequestSchema.parse(data);
};

export const validateUserSearch = (data: any): UserSearchRequestDto => {
  return UserSearchRequestSchema.parse(data);
};

export const validateLogin = (data: any): LoginRequestDto => {
  return LoginRequestSchema.parse(data);
};

export const validateRegister = (data: any): RegisterRequestDto => {
  return RegisterRequestSchema.parse(data);
};

export const validateForgotPassword = (data: any): ForgotPasswordRequestDto => {
  return ForgotPasswordRequestSchema.parse(data);
};

export const validateResetPassword = (data: any): ResetPasswordRequestDto => {
  return ResetPasswordRequestSchema.parse(data);
};

export const validateVerifyEmail = (data: any): VerifyEmailRequestDto => {
  return VerifyEmailRequestSchema.parse(data);
};
