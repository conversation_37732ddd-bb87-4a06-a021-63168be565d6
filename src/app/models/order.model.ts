/**
 * Order Model
 * Business entity cho Order
 */

import {
  BaseEntity,
  OrderStatus,
  PaymentStatus,
  AddressData,
} from "./common.model";

/**
 * Order Entity
 */
export interface OrderEntity extends BaseEntity {
  orderNumber: string;
  userId: string;
  status: OrderStatus;
  paymentStatus: PaymentStatus;
  subtotal: number;
  tax: number;
  shipping: number;
  discount: number;
  total: number;
  currency: string;
  shippingAddress: AddressData;
  billingAddress?: AddressData;
  notes?: string;
  trackingNumber?: string;
  shippedAt?: Date;
  deliveredAt?: Date;
  cancelledAt?: Date;
  refundedAt?: Date;
  metadata?: Record<string, any>;
  items: OrderItemEntity[];
}

/**
 * Order Item Entity
 */
export interface OrderItemEntity extends BaseEntity {
  orderId: string;
  productId: string;
  productName: string;
  productSku: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  productSnapshot?: ProductSnapshot;
}

/**
 * Product Snapshot (lưu thông tin sản phẩm tại thời điểm đặt hàng)
 */
export interface ProductSnapshot {
  name: string;
  description: string;
  price: number;
  salePrice?: number;
  sku: string;
  images: string[];
  attributes?: Record<string, string>;
}

/**
 * Order with Relations
 */
export interface OrderWithRelations extends OrderEntity {
  user?: any; // UserEntity
  items?: OrderItemEntity[];
  payments?: OrderPayment[];
  shipments?: OrderShipment[];
  statusHistory?: OrderStatusHistory[];
}

/**
 * Order Payment
 */
export interface OrderPayment {
  id: string;
  orderId: string;
  amount: number;
  currency: string;
  method: PaymentMethod;
  status: PaymentStatus;
  transactionId?: string;
  gatewayResponse?: Record<string, any>;
  processedAt?: Date;
  failedAt?: Date;
  refundedAt?: Date;
  refundAmount?: number;
}

export enum PaymentMethod {
  CREDIT_CARD = "CREDIT_CARD",
  DEBIT_CARD = "DEBIT_CARD",
  PAYPAL = "PAYPAL",
  BANK_TRANSFER = "BANK_TRANSFER",
  CASH_ON_DELIVERY = "CASH_ON_DELIVERY",
  DIGITAL_WALLET = "DIGITAL_WALLET",
}

/**
 * Order Shipment
 */
export interface OrderShipment {
  id: string;
  orderId: string;
  carrier: string;
  trackingNumber: string;
  method: ShippingMethod;
  cost: number;
  estimatedDelivery?: Date;
  shippedAt?: Date;
  deliveredAt?: Date;
  items: OrderItemEntity[];
}

export enum ShippingMethod {
  STANDARD = "STANDARD",
  EXPRESS = "EXPRESS",
  OVERNIGHT = "OVERNIGHT",
  PICKUP = "PICKUP",
}

/**
 * Order Status History
 */
export interface OrderStatusHistory {
  id: string;
  orderId: string;
  fromStatus?: OrderStatus;
  toStatus: OrderStatus;
  reason?: string;
  notes?: string;
  changedBy?: string;
  timestamp: Date;
}

/**
 * Order Creation Data
 */
export interface CreateOrderData {
  userId: string;
  items: CreateOrderItemData[];
  shippingAddress: AddressData;
  billingAddress?: AddressData;
  paymentMethod: PaymentMethod;
  shippingMethod: ShippingMethod;
  notes?: string;
  couponCode?: string;
  discountCode?: string;
  cartId?: string;
}

export interface CreateOrderItemData {
  productId: string;
  quantity: number;
  price: number;
  unitPrice?: number; // Optional, will be fetched from product if not provided
}

/**
 * Order Update Data
 */
export interface UpdateOrderData {
  status?: OrderStatus;
  paymentStatus?: PaymentStatus;
  trackingNumber?: string;
  notes?: string;
  shippingAddress?: AddressData;
  billingAddress?: AddressData;
}

/**
 * Order Search Filters
 */
export interface OrderSearchFilters {
  search?: string; // Search by order number, customer name, email
  userId?: string;
  status?: OrderStatus;
  paymentStatus?: PaymentStatus;
  dateFrom?: Date;
  dateTo?: Date;
  amountMin?: number;
  amountMax?: number;
  paymentMethod?: PaymentMethod;
  shippingMethod?: ShippingMethod;
}

/**
 * Order Statistics
 */
export interface OrderStats {
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  pendingOrders: number;
  processingOrders: number;
  shippedOrders: number;
  deliveredOrders: number;
  cancelledOrders: number;
  refundedOrders: number;
}

/**
 * Order Business Rules
 */
export class OrderBusinessRules {
  static generateOrderNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `ORD-${timestamp.slice(-8)}-${random}`;
  }

  static validateOrderItems(items: CreateOrderItemData[]): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!items || items.length === 0) {
      errors.push("Order must have at least one item");
    }

    items.forEach((item, index) => {
      if (!item.productId) {
        errors.push(`Item ${index + 1}: Product ID is required`);
      }

      if (!item.quantity || item.quantity <= 0) {
        errors.push(`Item ${index + 1}: Quantity must be greater than 0`);
      }

      if (item.unitPrice !== undefined && item.unitPrice <= 0) {
        errors.push(`Item ${index + 1}: Unit price must be greater than 0`);
      }
    });

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static canUpdateOrder(order: OrderEntity, updatedBy: any): boolean {
    // Cannot update delivered, cancelled, or refunded orders
    if (
      [
        OrderStatus.DELIVERED,
        OrderStatus.CANCELLED,
        OrderStatus.REFUNDED,
      ].includes(order.status)
    ) {
      return false;
    }

    // Admin can always update
    if (updatedBy.role === "ADMIN" || updatedBy.role === "SUPER_ADMIN") {
      return true;
    }

    // Customer can only update pending orders
    return (
      order.userId === updatedBy.id && order.status === OrderStatus.PENDING
    );
  }

  static canCancelOrder(order: OrderEntity, cancelledBy: any): boolean {
    // Cannot cancel delivered or already cancelled orders
    if (
      [
        OrderStatus.DELIVERED,
        OrderStatus.CANCELLED,
        OrderStatus.REFUNDED,
      ].includes(order.status)
    ) {
      return false;
    }

    // Admin can always cancel
    if (cancelledBy.role === "ADMIN" || cancelledBy.role === "SUPER_ADMIN") {
      return true;
    }

    // Customer can only cancel pending or confirmed orders
    return (
      order.userId === cancelledBy.id &&
      [OrderStatus.PENDING, OrderStatus.CONFIRMED].includes(order.status)
    );
  }

  static calculateTotals(
    items: CreateOrderItemData[],
    shipping: number = 0,
    tax: number = 0,
    discount: number = 0
  ) {
    const subtotal = items.reduce((sum, item) => {
      const unitPrice = item.unitPrice || 0;
      return sum + unitPrice * item.quantity;
    }, 0);

    const total = subtotal + shipping + tax - discount;

    return {
      subtotal,
      shipping,
      tax,
      discount,
      total,
    };
  }

  static getNextStatus(currentStatus: OrderStatus): OrderStatus[] {
    const statusFlow: Record<OrderStatus, OrderStatus[]> = {
      [OrderStatus.PENDING]: [OrderStatus.CONFIRMED, OrderStatus.CANCELLED],
      [OrderStatus.CONFIRMED]: [OrderStatus.PROCESSING, OrderStatus.CANCELLED],
      [OrderStatus.PROCESSING]: [OrderStatus.SHIPPED, OrderStatus.CANCELLED],
      [OrderStatus.SHIPPED]: [OrderStatus.DELIVERED],
      [OrderStatus.DELIVERED]: [OrderStatus.REFUNDED],
      [OrderStatus.CANCELLED]: [],
      [OrderStatus.REFUNDED]: [],
    };

    return statusFlow[currentStatus] || [];
  }

  static canTransitionTo(
    currentStatus: OrderStatus,
    newStatus: OrderStatus
  ): boolean {
    const allowedTransitions = this.getNextStatus(currentStatus);
    return allowedTransitions.includes(newStatus);
  }

  static isRefundable(order: OrderEntity): boolean {
    return (
      order.status === OrderStatus.DELIVERED &&
      order.paymentStatus === PaymentStatus.PAID
    );
  }

  static getRefundableAmount(order: OrderEntity): number {
    if (!this.isRefundable(order)) return 0;

    // Calculate refundable amount (could be partial based on business rules)
    return order.total;
  }

  static calculateTax(subtotal: number): number {
    // Simple tax calculation - 10%
    return subtotal * 0.1;
  }

  static calculateShipping(subtotal: number, address: any): number {
    // Simple shipping calculation
    if (subtotal > 100) return 0; // Free shipping over $100
    return 10; // Flat rate shipping
  }

  static canTransitionStatus(
    currentStatus: OrderStatus,
    newStatus: OrderStatus
  ): boolean {
    return this.canTransitionTo(currentStatus, newStatus);
  }

  static canCancel(order: OrderEntity): boolean {
    return [OrderStatus.PENDING, OrderStatus.CONFIRMED].includes(order.status);
  }

  static generateTrackingNumber(): string {
    const timestamp = Date.now().toString();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `TRK-${timestamp.slice(-8)}-${random}`;
  }
}
