/**
 * Contact Model
 * Business entity cho Contact
 */

import { BaseEntity, Status } from "./common.model";

/**
 * Contact Entity
 */
export interface ContactEntity extends BaseEntity {
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  status: ContactStatus;
  priority: ContactPriority;
  assignedTo?: string;
  response?: string;
  respondedAt?: Date;
  metadata?: Record<string, any>;
}

/**
 * Contact Status
 */
export enum ContactStatus {
  NEW = "NEW",
  IN_PROGRESS = "IN_PROGRESS",
  RESOLVED = "RESOLVED",
  CLOSED = "CLOSED",
}

/**
 * Contact Priority
 */
export enum ContactPriority {
  LOW = "LOW",
  MEDIUM = "MEDIUM",
  HIGH = "HIGH",
  URGENT = "URGENT",
}

/**
 * Create Contact Data
 */
export interface CreateContactData {
  name: string;
  email: string;
  phone?: string;
  subject: string;
  message: string;
  priority?: ContactPriority;
}

/**
 * Update Contact Data
 */
export interface UpdateContactData {
  status?: ContactStatus;
  priority?: ContactPriority;
  assignedTo?: string;
  response?: string;
}

/**
 * Contact Business Rules
 */
export class ContactBusinessRules {
  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  static validatePhone(phone: string): boolean {
    if (!phone) return true; // Phone is optional
    const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/;
    return phoneRegex.test(phone.replace(/[\s\-\(\)]/g, ""));
  }

  static validateContact(data: CreateContactData): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Validate name
    if (!data.name?.trim()) {
      errors.push("Name is required");
    } else if (data.name.trim().length < 2) {
      errors.push("Name must be at least 2 characters");
    }

    // Validate email
    if (!data.email?.trim()) {
      errors.push("Email is required");
    } else if (!this.validateEmail(data.email)) {
      errors.push("Invalid email format");
    }

    // Validate phone if provided
    if (data.phone && !this.validatePhone(data.phone)) {
      errors.push("Invalid phone number format");
    }

    // Validate subject
    if (!data.subject?.trim()) {
      errors.push("Subject is required");
    } else if (data.subject.trim().length < 5) {
      errors.push("Subject must be at least 5 characters");
    }

    // Validate message
    const messageValidation = this.validateMessage(data.message);
    if (!messageValidation.valid) {
      errors.push(...messageValidation.errors);
    }

    // Validate priority if provided
    if (
      data.priority &&
      !Object.values(ContactPriority).includes(data.priority)
    ) {
      errors.push("Invalid priority value");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static validateMessage(message: string): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!message || message.trim().length === 0) {
      errors.push("Message is required");
    }

    if (message.length < 10) {
      errors.push("Message must be at least 10 characters");
    }

    if (message.length > 2000) {
      errors.push("Message must be less than 2000 characters");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static getPriorityLevel(priority: ContactPriority): number {
    switch (priority) {
      case ContactPriority.URGENT:
        return 4;
      case ContactPriority.HIGH:
        return 3;
      case ContactPriority.MEDIUM:
        return 2;
      case ContactPriority.LOW:
        return 1;
      default:
        return 2;
    }
  }

  static getStatusLabel(status: ContactStatus): string {
    switch (status) {
      case ContactStatus.NEW:
        return "New";
      case ContactStatus.IN_PROGRESS:
        return "In Progress";
      case ContactStatus.RESOLVED:
        return "Resolved";
      case ContactStatus.CLOSED:
        return "Closed";
      default:
        return "Unknown";
    }
  }

  static getPriorityLabel(priority: ContactPriority): string {
    switch (priority) {
      case ContactPriority.LOW:
        return "Low";
      case ContactPriority.MEDIUM:
        return "Medium";
      case ContactPriority.HIGH:
        return "High";
      case ContactPriority.URGENT:
        return "Urgent";
      default:
        return "Medium";
    }
  }

  static canAutoClose(contact: ContactEntity): boolean {
    // Auto close if resolved for more than 7 days
    if (contact.status === ContactStatus.RESOLVED && contact.respondedAt) {
      const daysSinceResolved = Math.floor(
        (Date.now() - contact.respondedAt.getTime()) / (1000 * 60 * 60 * 24)
      );
      return daysSinceResolved >= 7;
    }
    return false;
  }

  static generateTicketNumber(): string {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return `CT-${timestamp}-${random}`.toUpperCase();
  }
}
