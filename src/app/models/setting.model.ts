/**
 * Setting Model
 * Business entity cho Setting
 */

import { BaseEntity } from './common.model';

/**
 * Setting Entity
 */
export interface SettingEntity extends BaseEntity {
  key: string;
  value: string;
  type: SettingType;
  category: string;
  description?: string;
  isPublic: boolean;
  metadata?: Record<string, any>;
}

/**
 * Setting Type
 */
export enum SettingType {
  STRING = 'STRING',
  NUMBER = 'NUMBER',
  BOOLEAN = 'BOOLEAN',
  JSON = 'JSON',
  TEXT = 'TEXT',
}

/**
 * Create Setting Data
 */
export interface CreateSettingData {
  key: string;
  value: string;
  type: SettingType;
  category: string;
  description?: string;
  isPublic?: boolean;
}

/**
 * Update Setting Data
 */
export interface UpdateSettingData {
  value?: string;
  description?: string;
  isPublic?: boolean;
}

/**
 * Setting Business Rules
 */
export class SettingBusinessRules {
  static validateKey(key: string): boolean {
    const keyRegex = /^[a-z0-9_]+$/;
    return keyRegex.test(key);
  }

  static parseValue(value: string, type: SettingType): any {
    switch (type) {
      case SettingType.NUMBER:
        return parseFloat(value);
      case SettingType.BOOLEAN:
        return value.toLowerCase() === 'true';
      case SettingType.JSON:
        try {
          return JSON.parse(value);
        } catch {
          return null;
        }
      default:
        return value;
    }
  }
}
