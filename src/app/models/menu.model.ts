/**
 * Menu Model
 * Business entity cho Menu
 */

import { BaseEntity, Status } from "./common.model";

/**
 * Menu Entity
 */
export interface MenuEntity extends BaseEntity {
  name: string;
  slug: string;
  location: MenuLocation;
  status: Status;
  sortOrder: number;
  metadata?: Record<string, any>;
}

/**
 * Menu Item Entity
 */
export interface MenuItemEntity extends BaseEntity {
  menuId: string;
  parentId?: string;
  title: string;
  url?: string;
  target: MenuTarget;
  icon?: string;
  sortOrder: number;
  status: Status;
  metadata?: Record<string, any>;
}

/**
 * Menu Location
 */
export enum MenuLocation {
  HEADER = "HEADER",
  FOOTER = "FOOTER",
  SIDEBAR = "SIDEBAR",
  MOBILE = "MOBILE",
}

/**
 * Menu Target
 */
export enum MenuTarget {
  SELF = "_self",
  BLANK = "_blank",
  PARENT = "_parent",
  TOP = "_top",
}

/**
 * Menu with Items
 */
export interface MenuWithItems extends MenuEntity {
  items?: MenuItemWithChildren[];
}

/**
 * Menu Item with Children
 */
export interface MenuItemWithChildren extends MenuItemEntity {
  children?: MenuItemWithChildren[];
}

/**
 * Create Menu Data
 */
export interface CreateMenuData {
  name: string;
  location: MenuLocation;
  status?: Status;
  sortOrder?: number;
}

/**
 * Update Menu Data
 */
export interface UpdateMenuData {
  name?: string;
  location?: MenuLocation;
  status?: Status;
  sortOrder?: number;
}

/**
 * Create Menu Item Data
 */
export interface CreateMenuItemData {
  menuId: string;
  parentId?: string;
  title: string;
  url?: string;
  target?: MenuTarget;
  icon?: string;
  sortOrder?: number;
  status?: Status;
}

/**
 * Update Menu Item Data
 */
export interface UpdateMenuItemData {
  parentId?: string;
  title?: string;
  url?: string;
  target?: MenuTarget;
  icon?: string;
  sortOrder?: number;
  status?: Status;
}

/**
 * Menu Business Rules
 */
export class MenuBusinessRules {
  static generateSlug(name: string): string {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, "")
      .replace(/\s+/g, "-")
      .replace(/-+/g, "-")
      .trim();
  }

  static buildMenuTree(items: MenuItemEntity[]): MenuItemWithChildren[] {
    const itemMap = new Map<string, MenuItemWithChildren>();
    const rootItems: MenuItemWithChildren[] = [];

    // Create map of all items
    items.forEach((item) => {
      itemMap.set(item.id, { ...item, children: [] });
    });

    // Build tree structure
    items.forEach((item) => {
      const menuItem = itemMap.get(item.id)!;

      if (item.parentId) {
        const parent = itemMap.get(item.parentId);
        if (parent) {
          parent.children!.push(menuItem);
        }
      } else {
        rootItems.push(menuItem);
      }
    });

    // Sort by sortOrder
    const sortItems = (items: MenuItemWithChildren[]) => {
      items.sort((a, b) => a.sortOrder - b.sortOrder);
      items.forEach((item) => {
        if (item.children && item.children.length > 0) {
          sortItems(item.children);
        }
      });
    };

    sortItems(rootItems);
    return rootItems;
  }

  static validateMenu(data: CreateMenuData | UpdateMenuData): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Validate name
    if ("name" in data && data.name !== undefined) {
      if (!data.name?.trim()) {
        errors.push("Menu name is required");
      } else if (data.name.trim().length < 2) {
        errors.push("Menu name must be at least 2 characters");
      } else if (data.name.trim().length > 100) {
        errors.push("Menu name must not exceed 100 characters");
      }
    }

    // Validate location
    if ("location" in data && data.location !== undefined) {
      if (!Object.values(MenuLocation).includes(data.location)) {
        errors.push("Invalid menu location");
      }
    }

    // Validate sortOrder
    if ("sortOrder" in data && data.sortOrder !== undefined) {
      if (data.sortOrder < 0) {
        errors.push("Sort order must be non-negative");
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static validateMenuItem(data: CreateMenuItemData | UpdateMenuItemData): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Validate title
    if ("title" in data && data.title !== undefined) {
      if (!data.title?.trim()) {
        errors.push("Menu item title is required");
      } else if (data.title.trim().length < 1) {
        errors.push("Menu item title must be at least 1 character");
      } else if (data.title.trim().length > 200) {
        errors.push("Menu item title must not exceed 200 characters");
      }
    }

    // Validate URL format if provided
    if ("url" in data && data.url) {
      if (!this.isValidUrl(data.url)) {
        errors.push("Invalid URL format");
      }
    }

    // Validate target
    if ("target" in data && data.target !== undefined) {
      if (!Object.values(MenuTarget).includes(data.target)) {
        errors.push("Invalid menu target");
      }
    }

    // Validate sortOrder
    if ("sortOrder" in data && data.sortOrder !== undefined) {
      if (data.sortOrder < 0) {
        errors.push("Sort order must be non-negative");
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static isValidUrl(url: string): boolean {
    // Allow relative URLs, absolute URLs, and anchors
    if (url.startsWith("/") || url.startsWith("#")) {
      return true;
    }

    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  static canDelete(
    menu: MenuEntity,
    itemCount: number
  ): {
    canDelete: boolean;
    reason?: string;
  } {
    if (itemCount > 0) {
      return {
        canDelete: false,
        reason: "Cannot delete menu with existing menu items",
      };
    }

    return { canDelete: true };
  }

  static canDeleteMenuItem(
    item: MenuItemEntity,
    childrenCount: number
  ): {
    canDelete: boolean;
    reason?: string;
  } {
    if (childrenCount > 0) {
      return {
        canDelete: false,
        reason: "Cannot delete menu item with children",
      };
    }

    return { canDelete: true };
  }

  static getLocationLabel(location: MenuLocation): string {
    const locationLabels: Record<MenuLocation, string> = {
      [MenuLocation.HEADER]: "Header",
      [MenuLocation.FOOTER]: "Footer",
      [MenuLocation.SIDEBAR]: "Sidebar",
      [MenuLocation.MOBILE]: "Mobile",
    };

    return locationLabels[location] || location;
  }

  static getTargetLabel(target: MenuTarget): string {
    const targetLabels: Record<MenuTarget, string> = {
      [MenuTarget.SELF]: "Same Window",
      [MenuTarget.BLANK]: "New Window",
      [MenuTarget.PARENT]: "Parent Frame",
      [MenuTarget.TOP]: "Top Frame",
    };

    return targetLabels[target] || target;
  }

  static getMaxDepth(): number {
    return 3; // Maximum nesting level for menu items
  }

  static validateDepth(
    parentId: string | undefined,
    items: MenuItemEntity[]
  ): boolean {
    if (!parentId) return true;

    let depth = 0;
    let currentParentId = parentId;

    while (currentParentId && depth < this.getMaxDepth()) {
      const parent = items.find((item) => item.id === currentParentId);
      if (!parent) break;

      currentParentId = parent.parentId;
      depth++;
    }

    return depth < this.getMaxDepth();
  }
}
