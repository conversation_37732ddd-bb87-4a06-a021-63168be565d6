/**
 * Admin User Model
 * Business entity cho Admin User
 */

import { BaseEntity, UserRole } from "./common.model";

/**
 * Admin Role Enum
 */
export enum AdminRole {
  ADMIN = "ADMIN",
  MODERATOR = "MODERATOR",
}

/**
 * Admin User Entity
 */
export interface AdminUserEntity extends BaseEntity {
  email: string;
  name: string;
  password?: string;
  avatar?: string;
  role: AdminRole;
  isActive: boolean;
  permissions: string[];
  lastLoginAt?: Date;
  metadata?: Record<string, any>;
}

/**
 * Admin User Creation Data
 */
export interface CreateAdminUserData {
  email: string;
  name: string;
  password: string;
  role: AdminRole;
  permissions?: string[];
  avatar?: string;
}

/**
 * Admin User Update Data
 */
export interface UpdateAdminUserData {
  name?: string;
  avatar?: string;
  role?: AdminRole;
  permissions?: string[];
  isActive?: boolean;
}

/**
 * Admin User Business Rules
 */
export class AdminUserBusinessRules {
  static readonly DEFAULT_PERMISSIONS = [
    "read:dashboard",
    "read:products",
    "read:orders",
    "read:customers",
  ];

  static readonly ADMIN_PERMISSIONS = [
    ...AdminUserBusinessRules.DEFAULT_PERMISSIONS,
    "write:products",
    "write:orders",
    "write:customers",
    "read:analytics",
  ];

  static readonly SUPER_ADMIN_PERMISSIONS = [
    ...AdminUserBusinessRules.ADMIN_PERMISSIONS,
    "write:admins",
    "write:settings",
    "write:permissions",
    "read:logs",
  ];

  static getDefaultPermissions(role: UserRole): string[] {
    switch (role) {
      case UserRole.SUPER_ADMIN:
        return this.SUPER_ADMIN_PERMISSIONS;
      case UserRole.ADMIN:
        return this.ADMIN_PERMISSIONS;
      default:
        return this.DEFAULT_PERMISSIONS;
    }
  }

  static canManageUser(
    admin: AdminUserEntity,
    targetUser: AdminUserEntity
  ): boolean {
    if (admin.role === UserRole.SUPER_ADMIN) return true;
    if (admin.role === UserRole.ADMIN && targetUser.role === UserRole.USER)
      return true;
    return false;
  }

  static validateAdminUser(data: CreateAdminUserData | UpdateAdminUserData): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // Validate email for create
    if ("email" in data) {
      if (!data.email?.trim()) {
        errors.push("Email is required");
      } else if (!this.validateEmail(data.email)) {
        errors.push("Invalid email format");
      }
    }

    // Validate name
    if ("name" in data && data.name !== undefined) {
      if (!data.name?.trim()) {
        errors.push("Name is required");
      } else if (data.name.trim().length < 2) {
        errors.push("Name must be at least 2 characters");
      }
    }

    // Validate password for create
    if ("password" in data) {
      if (!data.password) {
        errors.push("Password is required");
      } else if (!this.validatePassword(data.password)) {
        errors.push(
          "Password must be at least 8 characters with uppercase, lowercase, number and special character"
        );
      }
    }

    // Validate role
    if ("role" in data && data.role !== undefined) {
      if (!Object.values(UserRole).includes(data.role)) {
        errors.push("Invalid role");
      }
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  static validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  static validatePassword(password: string): boolean {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special character
    const passwordRegex =
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    return passwordRegex.test(password);
  }

  static async hashPassword(password: string): Promise<string> {
    // In a real app, use bcrypt or similar
    // For now, return a simple hash (this should be replaced with proper hashing)
    const crypto = await import("crypto");
    return crypto
      .createHash("sha256")
      .update(password + "salt")
      .digest("hex");
  }

  static async verifyPassword(
    password: string,
    hashedPassword: string
  ): Promise<boolean> {
    // In a real app, use bcrypt.compare or similar
    const hashedInput = await this.hashPassword(password);
    return hashedInput === hashedPassword;
  }

  static canAssignRole(
    admin: AdminUserEntity,
    _targetRole: AdminRole
  ): boolean {
    // Admin can assign any role
    if (admin.role === AdminRole.ADMIN) return true;

    // Moderator cannot assign roles
    return false;
  }

  static canDelete(
    admin: AdminUserEntity,
    targetUser: AdminUserEntity
  ): {
    canDelete: boolean;
    reason?: string;
  } {
    // Cannot delete yourself
    if (admin.id === targetUser.id) {
      return {
        canDelete: false,
        reason: "Cannot delete your own account",
      };
    }

    // Super admin can delete anyone except other super admins
    if (admin.role === UserRole.SUPER_ADMIN) {
      if (targetUser.role === UserRole.SUPER_ADMIN) {
        return {
          canDelete: false,
          reason: "Cannot delete other super admin accounts",
        };
      }
      return { canDelete: true };
    }

    // Admin can only delete regular users
    if (admin.role === UserRole.ADMIN && targetUser.role === UserRole.USER) {
      return { canDelete: true };
    }

    return {
      canDelete: false,
      reason: "Insufficient permissions to delete this user",
    };
  }

  static hasPermission(user: AdminUserEntity, permission: string): boolean {
    return (
      user.permissions.includes(permission) ||
      user.permissions.includes("*") ||
      user.role === UserRole.SUPER_ADMIN
    );
  }

  static generateTempPassword(): string {
    const chars =
      "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789@$!%*?&";
    let password = "";

    // Ensure at least one of each required character type
    password += "ABCDEFGHIJKLMNOPQRSTUVWXYZ"[Math.floor(Math.random() * 26)]; // uppercase
    password += "abcdefghijklmnopqrstuvwxyz"[Math.floor(Math.random() * 26)]; // lowercase
    password += "0123456789"[Math.floor(Math.random() * 10)]; // number
    password += "@$!%*?&"[Math.floor(Math.random() * 7)]; // special

    // Fill the rest randomly
    for (let i = 4; i < 12; i++) {
      password += chars[Math.floor(Math.random() * chars.length)];
    }

    // Shuffle the password
    return password
      .split("")
      .sort(() => Math.random() - 0.5)
      .join("");
  }
}
