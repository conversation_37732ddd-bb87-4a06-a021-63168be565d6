import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/route";
import { z } from "zod";
import { getProductRepository } from "../repositories/repository.factory";

const productSchema = z.object({
  name: z.string().min(1, "Tên sản phẩm là bắt buộc"),
  description: z.string().min(1, "Mô tả sản phẩm là bắt buộc"),
  price: z.number().positive("Giá phải lớn hơn 0"),
  salePrice: z.number().positive().optional(),
  images: z.array(z.string()).min(1, "Phải có ít nhất 1 hình ảnh"),
  categoryId: z.string().min(1, "Danh mục là bắt buộc"),
  stock: z.number().int().min(0, "<PERSON><PERSON> lượng không được âm"),
  sku: z.string().min(1, "SKU là bắt buộc"),
  featured: z.boolean().optional(),
  tags: z.array(z.string()).optional(),
});

// GET /api/products - Lấy danh sách sản phẩm
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "12");
    const category = searchParams.get("category");
    const search = searchParams.get("search");
    const featured = searchParams.get("featured") === "true";
    const sortBy = searchParams.get("sortBy") || "createdAt";
    const sortOrder = (searchParams.get("sortOrder") || "desc") as
      | "asc"
      | "desc";

    const productRepository = getProductRepository();

    // Use repository method for searching products
    const result = await productRepository.searchProducts({
      search: search || undefined,
      categoryId: category || undefined,
      featured: featured || undefined,
      status: "ACTIVE",
      page,
      limit,
      sortBy,
      sortOrder,
    });

    return NextResponse.json(result);
  } catch (error) {
    console.error("Get products error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy danh sách sản phẩm" },
      { status: 500 }
    );
  }
}

// POST /api/products - Tạo sản phẩm mới (Admin only)
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session || session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Không có quyền truy cập" },
        { status: 403 }
      );
    }

    const body = await request.json();
    const data = productSchema.parse(body);

    const productRepository = getProductRepository();

    // Use repository method to create product with auto-generated slug
    const product = await productRepository.createProductWithAutoSlug({
      name: data.name,
      description: data.description,
      price: data.price,
      salePrice: data.salePrice,
      category: { connect: { id: data.categoryId } },
      stock: data.stock,
      sku: data.sku,
      featured: data.featured || false,
      tags: data.tags || [],
      status: "ACTIVE",
    });

    return NextResponse.json(
      {
        message: "Tạo sản phẩm thành công",
        product,
      },
      { status: 201 }
    );
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    console.error("Create product error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi tạo sản phẩm" },
      { status: 500 }
    );
  }
}
