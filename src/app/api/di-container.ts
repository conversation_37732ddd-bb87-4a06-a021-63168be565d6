/**
 * Dependency Injection Container
 * Quản lý việc inject dependencies cho services và repositories
 */

import "reflect-metadata";

export type Constructor<T = {}> = new (...args: any[]) => T;
export type ServiceIdentifier<T = any> = string | symbol | Constructor<T>;
export type Factory<T = any> = () => T;

export interface ServiceDefinition<T = any> {
  factory: Factory<T>;
  singleton: boolean;
  instance?: T;
}

/**
 * Simple Dependency Injection Container
 */
export class DIContainer {
  private services = new Map<ServiceIdentifier, ServiceDefinition>();
  private static instance: DIContainer;

  /**
   * Singleton pattern để đảm bảo chỉ có 1 container instance
   */
  static getInstance(): DIContainer {
    if (!DIContainer.instance) {
      DIContainer.instance = new DIContainer();
    }
    return DIContainer.instance;
  }

  /**
   * Đăng ký service với container
   */
  register<T>(
    identifier: ServiceIdentifier<T>,
    factory: Factory<T>,
    options: { singleton?: boolean } = {}
  ): void {
    const { singleton = true } = options;

    this.services.set(identifier, {
      factory,
      singleton,
    });
  }

  /**
   * Đăng ký service với constructor
   */
  registerClass<T>(
    identifier: ServiceIdentifier<T>,
    constructor: Constructor<T>,
    dependencies: ServiceIdentifier[] = [],
    options: { singleton?: boolean } = {}
  ): void {
    const factory = () => {
      const deps = dependencies.map((dep) => this.resolve(dep));
      return new constructor(...deps);
    };

    this.register(identifier, factory, options);
  }

  /**
   * Resolve service từ container
   */
  resolve<T>(identifier: ServiceIdentifier<T>): T {
    const serviceDefinition = this.services.get(identifier);

    if (!serviceDefinition) {
      throw new Error(`Service ${String(identifier)} not found in container`);
    }

    // Nếu là singleton và đã có instance, trả về instance đó
    if (serviceDefinition.singleton && serviceDefinition.instance) {
      return serviceDefinition.instance;
    }

    // Tạo instance mới
    const instance = serviceDefinition.factory();

    // Lưu instance nếu là singleton
    if (serviceDefinition.singleton) {
      serviceDefinition.instance = instance;
    }

    return instance;
  }

  /**
   * Kiểm tra service đã được đăng ký chưa
   */
  has(identifier: ServiceIdentifier): boolean {
    return this.services.has(identifier);
  }

  /**
   * Xóa service khỏi container
   */
  remove(identifier: ServiceIdentifier): boolean {
    return this.services.delete(identifier);
  }

  /**
   * Clear tất cả services
   */
  clear(): void {
    this.services.clear();
  }

  /**
   * Lấy danh sách tất cả service identifiers
   */
  getRegisteredServices(): ServiceIdentifier[] {
    return Array.from(this.services.keys());
  }
}

/**
 * Service Identifiers - Symbols để tránh collision
 */
export const SERVICE_IDENTIFIERS = {
  // Repositories
  USER_REPOSITORY: Symbol("UserRepository"),
  ADMIN_USER_REPOSITORY: Symbol("AdminUserRepository"),
  PRODUCT_REPOSITORY: Symbol("ProductRepository"),
  CATEGORY_REPOSITORY: Symbol("CategoryRepository"),
  BRAND_REPOSITORY: Symbol("BrandRepository"),
  ORDER_REPOSITORY: Symbol("OrderRepository"),
  CART_REPOSITORY: Symbol("CartRepository"),
  ADDRESS_REPOSITORY: Symbol("AddressRepository"),
  REVIEW_REPOSITORY: Symbol("ReviewRepository"),
  WISHLIST_REPOSITORY: Symbol("WishlistRepository"),
  POST_REPOSITORY: Symbol("PostRepository"),
  PAGE_REPOSITORY: Symbol("PageRepository"),
  MEDIA_REPOSITORY: Symbol("MediaRepository"),
  MENU_REPOSITORY: Symbol("MenuRepository"),
  SETTING_REPOSITORY: Symbol("SettingRepository"),
  NOTIFICATION_REPOSITORY: Symbol("NotificationRepository"),
  AUDIT_LOG_REPOSITORY: Symbol("AuditLogRepository"),
  CONTACT_REPOSITORY: Symbol("ContactRepository"),
  ATTRIBUTE_REPOSITORY: Symbol("AttributeRepository"),
  INVENTORY_REPOSITORY: Symbol("InventoryRepository"),
  PROMOTION_REPOSITORY: Symbol("PromotionRepository"),

  // Services
  USER_SERVICE: Symbol("UserService"),
  ADMIN_USER_SERVICE: Symbol("AdminUserService"),
  PRODUCT_SERVICE: Symbol("ProductService"),
  CATEGORY_SERVICE: Symbol("CategoryService"),
  BRAND_SERVICE: Symbol("BrandService"),
  ORDER_SERVICE: Symbol("OrderService"),
  CART_SERVICE: Symbol("CartService"),
  ADDRESS_SERVICE: Symbol("AddressService"),
  REVIEW_SERVICE: Symbol("ReviewService"),
  WISHLIST_SERVICE: Symbol("WishlistService"),
  POST_SERVICE: Symbol("PostService"),
  PAGE_SERVICE: Symbol("PageService"),
  MEDIA_SERVICE: Symbol("MediaService"),
  MENU_SERVICE: Symbol("MenuService"),
  SETTING_SERVICE: Symbol("SettingService"),
  NOTIFICATION_SERVICE: Symbol("NotificationService"),
  AUDIT_LOG_SERVICE: Symbol("AuditLogService"),
  CONTACT_SERVICE: Symbol("ContactService"),
  ATTRIBUTE_SERVICE: Symbol("AttributeService"),
  INVENTORY_SERVICE: Symbol("InventoryService"),
  PROMOTION_SERVICE: Symbol("PromotionService"),
  AUTH_SERVICE: Symbol("AuthService"),
  EMAIL_SERVICE: Symbol("EmailService"),
  SEARCH_SERVICE: Symbol("SearchService"),
} as const;

/**
 * Decorator để inject dependencies
 */
export function Injectable<T extends Constructor>(constructor: T) {
  return class extends constructor {
    constructor(...args: any[]) {
      super(...args);
    }
  };
}

/**
 * Decorator để inject specific dependency
 */
export function Inject(identifier: ServiceIdentifier) {
  return function (
    target: any,
    propertyKey: string | symbol | undefined,
    parameterIndex: number
  ) {
    // Metadata để lưu thông tin dependency
    const existingTokens =
      Reflect.getMetadata("design:paramtypes", target) || [];
    existingTokens[parameterIndex] = identifier;
    Reflect.defineMetadata("design:paramtypes", existingTokens, target);
  };
}

/**
 * Helper function để lấy container instance
 */
export const container = DIContainer.getInstance();

/**
 * Helper function để resolve service
 */
export function resolve<T>(identifier: ServiceIdentifier<T>): T {
  return container.resolve(identifier);
}

/**
 * Helper function để register service
 */
export function register<T>(
  identifier: ServiceIdentifier<T>,
  factory: Factory<T>,
  options?: { singleton?: boolean }
): void {
  container.register(identifier, factory, options);
}
