/**
 * Admin User Service
 * Business logic cho Admin User management
 */

import { BaseService } from "./base.service";
import { Injectable } from "../di-container";
import { AdminUserRepository } from "../repositories";
import {
  AdminUserEntity,
  CreateAdminUserData,
  UpdateAdminUserData,
  AdminUserBusinessRules,
} from "../../models/admin-user.model";
import {
  PaginatedResult,
  SearchFilters,
  NotFoundError,
  ValidationError,
  ConflictError,
  ForbiddenError,
} from "../../models/common.model";
import { UserEntity } from "../../models/user.model";

// Service identifier
export const ADMIN_USER_SERVICE = Symbol("AdminUserService");

@Injectable
export class AdminUserService extends BaseService {
  private adminUserRepository: AdminUserRepository;

  constructor(adminUserRepository: AdminUserRepository) {
    super();
    this.adminUserRepository = adminUserRepository;
  }

  /**
   * Tạo admin user mới
   */
  async createAdminUser(
    data: CreateAdminUserData,
    createdBy: UserEntity
  ): Promise<AdminUserEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check super admin permission
      if (!this.isSuperAdmin(createdBy)) {
        throw new ForbiddenError("Only super admins can create admin users");
      }

      // Validate input
      this.validateRequired(data, ["email", "name", "role"]);

      // Validate admin user data
      const validation = AdminUserBusinessRules.validateAdminUser(data);
      if (!validation.valid) {
        throw new ValidationError(validation.errors.join(", "));
      }

      // Check email uniqueness
      const existingAdmin = await this.adminUserRepository.findByEmail(
        data.email
      );
      if (existingAdmin) {
        throw new ConflictError("Admin user with this email already exists");
      }

      // Validate role permissions
      if (!AdminUserBusinessRules.canAssignRole(createdBy as any, data.role)) {
        throw new ForbiddenError(`Cannot assign role '${data.role}'`);
      }

      // Hash password
      const hashedPassword = await AdminUserBusinessRules.hashPassword(
        data.password
      );

      // Create admin user
      const adminUserData = {
        email: data.email,
        name: data.name,
        password: hashedPassword,
        role: data.role,
        permissions: data.permissions,
        avatarId: data.avatar || undefined,
        isActive: true,
        lastLoginAt: null,
      };

      const adminUser = (await this.adminUserRepository.create(
        adminUserData
      )) as unknown as AdminUserEntity;

      // Log activity
      await this.logActivity("ADMIN_USER_CREATED", createdBy.id, {
        adminUserId: adminUser.id,
        email: adminUser.email,
        role: adminUser.role,
      });

      // Remove password from response
      const { password: _, ...adminUserResponse } = adminUser;
      return adminUserResponse as AdminUserEntity;
    }, "createAdminUser");
  }

  /**
   * Lấy admin user theo ID
   */
  async getAdminUserById(
    id: string,
    requestedBy: UserEntity
  ): Promise<AdminUserEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Only admins can access admin user details");
      }

      const adminUser = await this.adminUserRepository.findById(id);
      if (!adminUser) {
        throw new NotFoundError("Admin user", id);
      }

      // Remove password from response
      const { password: _, ...adminUserResponse } = adminUser;
      return adminUserResponse as unknown as AdminUserEntity;
    }, "getAdminUserById");
  }

  /**
   * Cập nhật admin user
   */
  async updateAdminUser(
    id: string,
    data: UpdateAdminUserData,
    updatedBy: UserEntity
  ): Promise<AdminUserEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check if admin user exists
      const existingAdminUser = await this.adminUserRepository.findById(id);
      if (!existingAdminUser) {
        throw new NotFoundError("Admin user", id);
      }

      // Check permission
      if (
        !this.canModifyAdminUser(
          updatedBy,
          existingAdminUser as unknown as AdminUserEntity
        )
      ) {
        throw new ForbiddenError("Cannot modify this admin user");
      }

      // Validate role change if provided
      if (data.role && data.role !== existingAdminUser.role) {
        if (
          !AdminUserBusinessRules.canAssignRole(updatedBy as any, data.role)
        ) {
          throw new ForbiddenError(`Cannot assign role '${data.role}'`);
        }
      }

      // Prepare update data
      let updateData = {
        name: data.name,
        role: data.role,
        permissions: data.permissions,
        isActive: data.isActive,
        avatarId: data.avatar || undefined,
      };

      // Update admin user
      const updatedAdminUser = (await this.adminUserRepository.update(
        id,
        updateData
      )) as unknown as AdminUserEntity;

      // Log activity
      await this.logActivity("ADMIN_USER_UPDATED", updatedBy.id, {
        adminUserId: id,
        changes: Object.keys(data),
      });

      // Remove password from response
      const { password: _, ...adminUserResponse } = updatedAdminUser;
      return adminUserResponse as AdminUserEntity;
    }, "updateAdminUser");
  }

  /**
   * Xóa admin user
   */
  async deleteAdminUser(id: string, deletedBy: UserEntity): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // Check if admin user exists
      const adminUser = await this.adminUserRepository.findById(id);
      if (!adminUser) {
        throw new NotFoundError("Admin user", id);
      }

      // Check permission
      if (
        !this.canModifyAdminUser(
          deletedBy,
          adminUser as unknown as AdminUserEntity
        )
      ) {
        throw new ForbiddenError("Cannot delete this admin user");
      }

      // Prevent self-deletion
      if (adminUser.id === deletedBy.id) {
        throw new ValidationError("Cannot delete your own account");
      }

      // Check if admin user can be deleted
      const deleteCheck = AdminUserBusinessRules.canDelete(
        deletedBy as unknown as AdminUserEntity,
        adminUser as unknown as AdminUserEntity
      );

      if (!deleteCheck.canDelete) {
        throw new ValidationError(
          deleteCheck.reason || "Cannot delete this admin user"
        );
      }

      // Delete admin user
      await this.adminUserRepository.delete(id);

      // Log activity
      await this.logActivity("ADMIN_USER_DELETED", deletedBy.id, {
        adminUserId: id,
        email: adminUser.email,
      });
    }, "deleteAdminUser");
  }

  /**
   * Lấy danh sách admin users
   */
  async getAdminUsers(
    filters: SearchFilters & { role?: string } = {},
    requestedBy: UserEntity
  ): Promise<PaginatedResult<AdminUserEntity>> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Only admins can list admin users");
      }

      // Validate pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      this.validatePagination(page, limit);

      // Build search conditions
      const searchConditions: any = {};

      if (filters.search) {
        const searchQuery = this.sanitizeSearchQuery(filters.search);
        searchConditions.OR = [
          { name: { contains: searchQuery, mode: "insensitive" } },
          { email: { contains: searchQuery, mode: "insensitive" } },
        ];
      }

      if (filters.role) {
        searchConditions.role = filters.role;
      }

      if (filters.status) {
        searchConditions.status = filters.status;
      }

      // Get admin users with pagination
      const result = await this.adminUserRepository.findWithPagination({
        page,
        limit,
        where: searchConditions,
        orderBy: {
          [filters.sortBy || "createdAt"]: filters.sortOrder || "desc",
        },
        select: {
          id: true,
          email: true,
          name: true,
          role: true,
          status: true,
          lastLoginAt: true,
          createdAt: true,
          updatedAt: true,
          // Exclude password
        },
      });

      return {
        data: result.data.map((admin) => admin as unknown as AdminUserEntity),
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNext: result.page < result.totalPages,
        hasPrev: result.page > 1,
      };
    }, "getAdminUsers");
  }

  /**
   * Authenticate admin user
   */
  async authenticateAdminUser(
    email: string,
    password: string
  ): Promise<AdminUserEntity | null> {
    return this.executeWithErrorHandling(async () => {
      // Find admin user by email
      const adminUser = await this.adminUserRepository.findByEmail(email);
      if (!adminUser) {
        return null;
      }

      // Check if account is active
      if (!adminUser.isActive) {
        throw new ValidationError("Account is not active");
      }

      // Verify password
      const isValidPassword = await AdminUserBusinessRules.verifyPassword(
        password,
        adminUser.password
      );
      if (!isValidPassword) {
        return null;
      }

      // Update last login
      await this.adminUserRepository.update(adminUser.id, {
        lastLoginAt: new Date(),
      });

      // Log activity
      await this.logActivity("ADMIN_USER_LOGIN", adminUser.id, {
        email: adminUser.email,
      });

      // Remove password from response
      const { password: _, ...adminUserResponse } = adminUser;
      return adminUserResponse as unknown as AdminUserEntity;
    }, "authenticateAdminUser");
  }

  /**
   * Change admin user password
   */
  async changePassword(
    id: string,
    currentPassword: string,
    newPassword: string,
    requestedBy: UserEntity
  ): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // Check if admin user exists
      const adminUser = await this.adminUserRepository.findById(id);
      if (!adminUser) {
        throw new NotFoundError("Admin user", id);
      }

      // Check permission (can only change own password unless super admin)
      if (adminUser.id !== requestedBy.id && !this.isSuperAdmin(requestedBy)) {
        throw new ForbiddenError(
          "Cannot change password for another admin user"
        );
      }

      // Verify current password
      const isValidPassword = await AdminUserBusinessRules.verifyPassword(
        currentPassword,
        adminUser.password
      );
      if (!isValidPassword) {
        throw new ValidationError("Current password is incorrect");
      }

      // Validate new password
      if (!AdminUserBusinessRules.validatePassword(newPassword)) {
        throw new ValidationError("New password does not meet requirements");
      }

      // Hash new password
      const hashedPassword =
        await AdminUserBusinessRules.hashPassword(newPassword);

      // Update password
      await this.adminUserRepository.update(id, {
        password: hashedPassword,
      });

      // Log activity
      await this.logActivity("ADMIN_USER_PASSWORD_CHANGED", requestedBy.id, {
        adminUserId: id,
      });
    }, "changePassword");
  }

  /**
   * Update admin user status
   */
  async updateAdminUserStatus(
    id: string,
    status: "ACTIVE" | "INACTIVE",
    updatedBy: UserEntity
  ): Promise<AdminUserEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check super admin permission
      if (!this.isSuperAdmin(updatedBy)) {
        throw new ForbiddenError(
          "Only super admins can update admin user status"
        );
      }

      const adminUser = await this.updateAdminUser(
        id,
        { isActive: status === "ACTIVE" },
        updatedBy
      );

      // Log activity
      await this.logActivity("ADMIN_USER_STATUS_UPDATED", updatedBy.id, {
        adminUserId: id,
        newStatus: status,
      });

      return adminUser;
    }, "updateAdminUserStatus");
  }

  /**
   * Helper methods
   */
  private isAdmin(user: UserEntity): boolean {
    return ["ADMIN", "SUPER_ADMIN"].includes(user.role);
  }

  private isSuperAdmin(user: UserEntity): boolean {
    return user.role === "SUPER_ADMIN";
  }

  private canModifyAdminUser(
    requestedBy: UserEntity,
    targetAdmin: AdminUserEntity
  ): boolean {
    // Super admin can modify anyone
    if (this.isSuperAdmin(requestedBy)) {
      return true;
    }

    // Admin can only modify themselves
    if (this.isAdmin(requestedBy) && requestedBy.id === targetAdmin.id) {
      return true;
    }

    return false;
  }
}
