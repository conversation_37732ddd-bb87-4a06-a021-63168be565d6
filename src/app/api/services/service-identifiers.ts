/**
 * Service Identifiers
 * Symbol-based identifiers for dependency injection
 */

// Core Services
export const BASE_SERVICE = Symbol("BaseService");
export const CACHE_SERVICE = Symbol("CacheService");
export const USER_SERVICE = Symbol("UserService");
export const PRODUCT_SERVICE = Symbol("ProductService");
export const CATEGORY_SERVICE = Symbol("CategoryService");
export const ORDER_SERVICE = Symbol("OrderService");
export const CART_SERVICE = Symbol("CartService");

// E-commerce Services
export const BRAND_SERVICE = Symbol("BrandService");
export const ADDRESS_SERVICE = Symbol("AddressService");
export const REVIEW_SERVICE = Symbol("ReviewService");
export const WISHLIST_SERVICE = Symbol("WishlistService");

// Content Management Services
export const MEDIA_SERVICE = Symbol("MediaService");
export const MENU_SERVICE = Symbol("MenuService");
export const POST_SERVICE = Symbol("PostService");
export const PAGE_SERVICE = Symbol("PageService");

// System & Admin Services
export const ADMIN_USER_SERVICE = Symbol("AdminUserService");
export const SETTING_SERVICE = Symbol("SettingService");
export const NOTIFICATION_SERVICE = Symbol("NotificationService");
export const AUDIT_LOG_SERVICE = Symbol("AuditLogService");

// Advanced Services
export const CONTACT_SERVICE = Symbol("ContactService");
export const ATTRIBUTE_SERVICE = Symbol("AttributeService");
export const INVENTORY_SERVICE = Symbol("InventoryService");
export const PROMOTION_SERVICE = Symbol("PromotionService");

// Event and Notification Services
export const EVENT_SERVICE = Symbol("EventService");
export const NOTIFICATION_HANDLERS_SERVICE = Symbol(
  "NotificationHandlersService"
);

// Real-time and Communication Services
export const WEBSOCKET_SERVICE = Symbol("WebSocketService");
export const EMAIL_SERVICE = Symbol("EmailService");
export const METRICS_SERVICE = Symbol("MetricsService");
export const RATE_LIMIT_SERVICE = Symbol("RateLimitService");

// Service identifier type
export type ServiceIdentifier<T = any> = symbol;

// Service registry type
export interface ServiceRegistry {
  [BASE_SERVICE]: any;
  [CACHE_SERVICE]: any;
  [USER_SERVICE]: any;
  [PRODUCT_SERVICE]: any;
  [CATEGORY_SERVICE]: any;
  [ORDER_SERVICE]: any;
  [CART_SERVICE]: any;
  [BRAND_SERVICE]: any;
  [ADDRESS_SERVICE]: any;
  [REVIEW_SERVICE]: any;
  [WISHLIST_SERVICE]: any;
  [MEDIA_SERVICE]: any;
  [MENU_SERVICE]: any;
  [POST_SERVICE]: any;
  [PAGE_SERVICE]: any;
  [ADMIN_USER_SERVICE]: any;
  [SETTING_SERVICE]: any;
  [NOTIFICATION_SERVICE]: any;
  [AUDIT_LOG_SERVICE]: any;
  [CONTACT_SERVICE]: any;
  [ATTRIBUTE_SERVICE]: any;
  [INVENTORY_SERVICE]: any;
  [PROMOTION_SERVICE]: any;
  [EVENT_SERVICE]: any;
  [NOTIFICATION_HANDLERS_SERVICE]: any;
  [WEBSOCKET_SERVICE]: any;
  [EMAIL_SERVICE]: any;
  [METRICS_SERVICE]: any;
  [RATE_LIMIT_SERVICE]: any;
}

// Helper type for service resolution
export type ServiceType<T extends ServiceIdentifier> =
  T extends keyof ServiceRegistry ? ServiceRegistry[T] : any;
