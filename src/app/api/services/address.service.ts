/**
 * Address Service
 * Business logic cho Address management
 */

import { BaseService } from "./base.service";
import { Injectable } from "../di-container";
import { AddressRepository, UserRepository } from "../repositories";
import {
  AddressEntity,
  CreateAddressData,
  UpdateAddressData,
  AddressBusinessRules,
} from "../../models/address.model";
import {
  PaginatedResult,
  SearchFilters,
  NotFoundError,
  ValidationError,
  ForbiddenError,
} from "../../models/common.model";
import { UserEntity } from "../../models/user.model";

// Service identifier
export const ADDRESS_SERVICE = Symbol("AddressService");

@Injectable
export class AddressService extends BaseService {
  private addressRepository: AddressRepository;
  private userRepository: UserRepository;

  constructor(
    addressRepository: AddressRepository,
    userRepository: UserRepository
  ) {
    super();
    this.addressRepository = addressRepository;
    this.userRepository = userRepository;
  }

  /**
   * Tạo address mới
   */
  async createAddress(
    data: CreateAddressData,
    createdBy: UserEntity
  ): Promise<AddressEntity> {
    return this.executeWithErrorHandling(async () => {
      // Validate input
      this.validateRequired(data, [
        "userId",
        "fullName",
        "phone",
        "address",
        "city",
        "country",
      ]);

      // Check if user exists
      const user = await this.userRepository.findById(data.userId);
      if (!user) {
        throw new NotFoundError("User", data.userId);
      }

      // Check permission
      if (createdBy.id !== data.userId && !this.isAdmin(createdBy)) {
        throw new ForbiddenError("Cannot create address for another user");
      }

      // Validate address data
      const validation = AddressBusinessRules.validateAddress(data);
      if (!validation.valid) {
        throw new ValidationError(validation.errors.join(", "));
      }

      // Check address limit per user
      const userAddressCount = await this.addressRepository.countByUser(
        data.userId
      );
      if (!AddressBusinessRules.canAddMoreAddresses(userAddressCount)) {
        throw new ValidationError(
          `Maximum ${AddressBusinessRules.MAX_ADDRESSES_PER_USER} addresses allowed per user`
        );
      }

      // If this is the first address or marked as default, set as default
      const isFirstAddress = userAddressCount === 0;

      // Transform data to match Prisma schema
      const addressData = {
        userId: data.userId,
        fullName: `${data.firstName} ${data.lastName}`,
        phone: data.phone || "",
        address: `${data.address1}${data.address2 ? ", " + data.address2 : ""}`,
        ward: data.address2 || "",
        district: data.city,
        province: data.state,
        isDefault: data.isDefault || isFirstAddress,
      };

      // If setting as default, unset other default addresses
      if (addressData.isDefault) {
        await this.addressRepository.unsetDefaultForUser(data.userId);
      }

      // Create address
      const address = (await this.addressRepository.create(
        addressData
      )) as unknown as AddressEntity;

      // Log activity
      await this.logActivity("ADDRESS_CREATED", createdBy.id, {
        addressId: address.id,
        userId: data.userId,
      });

      return address;
    }, "createAddress");
  }

  /**
   * Lấy address theo ID
   */
  async getAddressById(
    id: string,
    requestedBy: UserEntity
  ): Promise<AddressEntity> {
    return this.executeWithErrorHandling(async () => {
      const address = await this.addressRepository.findById(id);
      if (!address) {
        throw new NotFoundError("Address", id);
      }

      // Check permission
      if (address.userId !== requestedBy.id && !this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Cannot access address of another user");
      }

      return address as unknown as AddressEntity;
    }, "getAddressById");
  }

  /**
   * Cập nhật address
   */
  async updateAddress(
    id: string,
    data: UpdateAddressData,
    updatedBy: UserEntity
  ): Promise<AddressEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check if address exists
      const existingAddress = await this.addressRepository.findById(id);
      if (!existingAddress) {
        throw new NotFoundError("Address", id);
      }

      // Check permission
      if (existingAddress.userId !== updatedBy.id && !this.isAdmin(updatedBy)) {
        throw new ForbiddenError("Cannot update address of another user");
      }

      // Validate address data if provided
      if (
        Object.keys(data).some((key) =>
          ["fullName", "phone", "address", "city", "country"].includes(key)
        )
      ) {
        const mergedData = { ...existingAddress, ...data };
        const validation = AddressBusinessRules.validateAddress(mergedData);
        if (!validation.valid) {
          throw new ValidationError(validation.errors.join(", "));
        }
      }

      // If setting as default, unset other default addresses
      if (data.isDefault) {
        await this.addressRepository.unsetDefaultForUser(
          existingAddress.userId
        );
      }

      // Update address
      const updatedAddress = (await this.addressRepository.update(
        id,
        data
      )) as unknown as AddressEntity;

      // Log activity
      await this.logActivity("ADDRESS_UPDATED", updatedBy.id, {
        addressId: id,
        changes: data,
      });

      return updatedAddress;
    }, "updateAddress");
  }

  /**
   * Xóa address
   */
  async deleteAddress(id: string, deletedBy: UserEntity): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // Check if address exists
      const address = await this.addressRepository.findById(id);
      if (!address) {
        throw new NotFoundError("Address", id);
      }

      // Check permission
      if (address.userId !== deletedBy.id && !this.isAdmin(deletedBy)) {
        throw new ForbiddenError("Cannot delete address of another user");
      }

      // Get user addresses to check deletion rules
      const userAddresses = await this.addressRepository.findByUser(
        address.userId
      );

      // Check if address can be deleted
      const deleteCheck = AddressBusinessRules.canDelete(
        address as unknown as AddressEntity,
        userAddresses as unknown as AddressEntity[]
      );

      if (!deleteCheck.canDelete) {
        throw new ValidationError(
          deleteCheck.reason || "Cannot delete address"
        );
      }

      // If deleting default address, set another address as default
      if (address.isDefault) {
        const otherAddresses = await this.addressRepository.findByUser(
          address.userId
        );
        const remainingAddresses = otherAddresses.filter(
          (addr) => addr.id !== id
        );

        if (remainingAddresses.length > 0) {
          await this.addressRepository.update(remainingAddresses[0].id, {
            isDefault: true,
          });
        }
      }

      // Delete address
      await this.addressRepository.delete(id);

      // Log activity
      await this.logActivity("ADDRESS_DELETED", deletedBy.id, {
        addressId: id,
        userId: address.userId,
      });
    }, "deleteAddress");
  }

  /**
   * Lấy addresses của user
   */
  async getUserAddresses(
    userId: string,
    requestedBy: UserEntity
  ): Promise<AddressEntity[]> {
    return this.executeWithErrorHandling(async () => {
      // Check permission
      if (userId !== requestedBy.id && !this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Cannot access addresses of another user");
      }

      const addresses = await this.addressRepository.findByUser(userId);
      return addresses.map((address) => address as unknown as AddressEntity);
    }, "getUserAddresses");
  }

  /**
   * Lấy default address của user
   */
  async getUserDefaultAddress(
    userId: string,
    requestedBy: UserEntity
  ): Promise<AddressEntity | null> {
    return this.executeWithErrorHandling(async () => {
      // Check permission
      if (userId !== requestedBy.id && !this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Cannot access addresses of another user");
      }

      const address = await this.addressRepository.findDefaultByUser(userId);
      return address ? (address as unknown as AddressEntity) : null;
    }, "getUserDefaultAddress");
  }

  /**
   * Set address làm default
   */
  async setDefaultAddress(
    id: string,
    requestedBy: UserEntity
  ): Promise<AddressEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check if address exists
      const address = await this.addressRepository.findById(id);
      if (!address) {
        throw new NotFoundError("Address", id);
      }

      // Check permission
      if (address.userId !== requestedBy.id && !this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Cannot modify address of another user");
      }

      // Unset other default addresses for this user
      await this.addressRepository.unsetDefaultForUser(address.userId);

      // Set this address as default
      const updatedAddress = (await this.addressRepository.update(id, {
        isDefault: true,
      })) as unknown as AddressEntity;

      // Log activity
      await this.logActivity("ADDRESS_SET_DEFAULT", requestedBy.id, {
        addressId: id,
        userId: address.userId,
      });

      return updatedAddress;
    }, "setDefaultAddress");
  }

  /**
   * Validate address format
   */
  async validateAddressFormat(data: CreateAddressData): Promise<{
    valid: boolean;
    errors: string[];
    suggestions?: any;
  }> {
    return this.executeWithErrorHandling(async () => {
      const validation = AddressBusinessRules.validateAddress(data);

      // TODO: Add external address validation service integration
      // const suggestions = await this.getAddressSuggestions(data);

      return {
        valid: validation.valid,
        errors: validation.errors,
        // suggestions
      };
    }, "validateAddressFormat");
  }

  /**
   * Search addresses (admin only)
   */
  async searchAddresses(
    filters: SearchFilters & { userId?: string },
    requestedBy: UserEntity
  ): Promise<PaginatedResult<AddressEntity>> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(requestedBy)) {
        throw new ForbiddenError("Only admins can search all addresses");
      }

      // Validate pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      this.validatePagination(page, limit);

      // Build search conditions
      const searchConditions: any = {};

      if (filters.search) {
        const searchQuery = this.sanitizeSearchQuery(filters.search);
        searchConditions.OR = [
          { fullName: { contains: searchQuery, mode: "insensitive" } },
          { address: { contains: searchQuery, mode: "insensitive" } },
          { city: { contains: searchQuery, mode: "insensitive" } },
        ];
      }

      if (filters.userId) {
        searchConditions.userId = filters.userId;
      }

      // Get addresses with pagination
      const result = await this.addressRepository.findWithPagination({
        page,
        limit,
        where: searchConditions,
        orderBy: {
          [filters.sortBy || "createdAt"]: filters.sortOrder || "desc",
        },
      });

      return {
        data: result.data.map((address) => address as unknown as AddressEntity),
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
        hasNext: result.page < result.totalPages,
        hasPrev: result.page > 1,
      };
    }, "searchAddresses");
  }

  /**
   * Helper method to check if user is admin
   */
  private isAdmin(user: UserEntity): boolean {
    return ["ADMIN", "SUPER_ADMIN"].includes(user.role);
  }
}
