/**
 * Cache Service
 * Provides caching functionality for services
 */

export interface CacheOptions {
  ttl?: number; // Time to live in seconds
  tags?: string[]; // Cache tags for invalidation
}

export interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  tags: string[];
}

export class CacheService {
  private cache = new Map<string, CacheEntry<any>>();
  private readonly defaultTTL = 300; // 5 minutes

  /**
   * Get value from cache
   */
  async get<T>(key: string): Promise<T | null> {
    const entry = this.cache.get(key);
    
    if (!entry) {
      return null;
    }

    // Check if expired
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      return null;
    }

    return entry.data;
  }

  /**
   * Set value in cache
   */
  async set<T>(key: string, data: T, options: CacheOptions = {}): Promise<void> {
    const ttl = options.ttl || this.defaultTTL;
    const tags = options.tags || [];

    const entry: CacheEntry<T> = {
      data,
      timestamp: Date.now(),
      ttl: ttl * 1000, // Convert to milliseconds
      tags
    };

    this.cache.set(key, entry);
  }

  /**
   * Delete value from cache
   */
  async delete(key: string): Promise<void> {
    this.cache.delete(key);
  }

  /**
   * Clear all cache
   */
  async clear(): Promise<void> {
    this.cache.clear();
  }

  /**
   * Invalidate cache by tags
   */
  async invalidateByTags(tags: string[]): Promise<void> {
    for (const [key, entry] of this.cache.entries()) {
      if (entry.tags.some(tag => tags.includes(tag))) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Get or set pattern - fetch data if not in cache
   */
  async getOrSet<T>(
    key: string,
    fetcher: () => Promise<T>,
    options: CacheOptions = {}
  ): Promise<T> {
    const cached = await this.get<T>(key);
    
    if (cached !== null) {
      return cached;
    }

    const data = await fetcher();
    await this.set(key, data, options);
    
    return data;
  }

  /**
   * Check if cache entry is expired
   */
  private isExpired(entry: CacheEntry<any>): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  /**
   * Get cache statistics
   */
  getStats(): {
    size: number;
    expired: number;
    hitRate: number;
  } {
    let expired = 0;
    
    for (const entry of this.cache.values()) {
      if (this.isExpired(entry)) {
        expired++;
      }
    }

    return {
      size: this.cache.size,
      expired,
      hitRate: 0 // Would need to track hits/misses for real hit rate
    };
  }

  /**
   * Clean up expired entries
   */
  cleanup(): void {
    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        this.cache.delete(key);
      }
    }
  }

  /**
   * Generate cache key with prefix
   */
  static generateKey(prefix: string, ...parts: (string | number)[]): string {
    return `${prefix}:${parts.join(':')}`;
  }

  /**
   * Cache TTL constants
   */
  static readonly TTL = {
    SHORT: 60,      // 1 minute
    MEDIUM: 300,    // 5 minutes
    LONG: 1800,     // 30 minutes
    VERY_LONG: 3600 // 1 hour
  } as const;

  /**
   * Cache tags constants
   */
  static readonly TAGS = {
    PRODUCTS: 'products',
    CATEGORIES: 'categories',
    USERS: 'users',
    ORDERS: 'orders',
    BRANDS: 'brands',
    REVIEWS: 'reviews',
    SETTINGS: 'settings',
    MENU: 'menu',
    PAGES: 'pages',
    POSTS: 'posts'
  } as const;
}

/**
 * Cache decorator for methods
 */
export function Cacheable(options: CacheOptions & { keyGenerator?: (...args: any[]) => string }) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const cacheService = this.cacheService as CacheService;
      
      if (!cacheService) {
        return method.apply(this, args);
      }

      const key = options.keyGenerator 
        ? options.keyGenerator(...args)
        : CacheService.generateKey(target.constructor.name, propertyName, ...args);

      return cacheService.getOrSet(key, () => method.apply(this, args), options);
    };
  };
}

/**
 * Cache invalidation decorator
 */
export function InvalidateCache(tags: string[]) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const result = await method.apply(this, args);
      
      const cacheService = this.cacheService as CacheService;
      if (cacheService) {
        await cacheService.invalidateByTags(tags);
      }

      return result;
    };
  };
}
