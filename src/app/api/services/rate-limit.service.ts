/**
 * Rate Limiting Service
 * Cache-based rate limiting with multiple strategies
 */

import { CacheService } from './cache.service';

export interface RateLimitConfig {
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Maximum requests per window
  strategy: 'sliding_window' | 'token_bucket' | 'fixed_window';
  keyGenerator?: (identifier: string) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  message?: string;
}

export interface RateLimitResult {
  allowed: boolean;
  remaining: number;
  resetTime: Date;
  totalRequests: number;
  message?: string;
}

export interface TokenBucketState {
  tokens: number;
  lastRefill: number;
  capacity: number;
  refillRate: number; // tokens per second
}

export class RateLimitService {
  private static instance: RateLimitService;
  private cacheService: CacheService;

  private constructor() {
    this.cacheService = CacheService.getInstance();
  }

  static getInstance(): RateLimitService {
    if (!RateLimitService.instance) {
      RateLimitService.instance = new RateLimitService();
    }
    return RateLimitService.instance;
  }

  /**
   * Check rate limit for identifier
   */
  async checkRateLimit(
    identifier: string,
    config: RateLimitConfig
  ): Promise<RateLimitResult> {
    const key = config.keyGenerator ? config.keyGenerator(identifier) : `rate_limit:${identifier}`;

    switch (config.strategy) {
      case 'sliding_window':
        return this.slidingWindowCheck(key, config);
      case 'token_bucket':
        return this.tokenBucketCheck(key, config);
      case 'fixed_window':
        return this.fixedWindowCheck(key, config);
      default:
        throw new Error(`Unknown rate limit strategy: ${config.strategy}`);
    }
  }

  /**
   * Sliding window rate limiting
   */
  private async slidingWindowCheck(
    key: string,
    config: RateLimitConfig
  ): Promise<RateLimitResult> {
    const now = Date.now();
    const windowStart = now - config.windowMs;
    const requestsKey = `${key}:requests`;

    // Get existing requests in the window
    const existingRequests = await this.cacheService.get<number[]>(requestsKey) || [];
    
    // Filter requests within the current window
    const validRequests = existingRequests.filter(timestamp => timestamp > windowStart);
    
    // Check if limit exceeded
    const allowed = validRequests.length < config.maxRequests;
    
    if (allowed) {
      // Add current request timestamp
      validRequests.push(now);
      await this.cacheService.set(requestsKey, validRequests, {
        ttl: Math.ceil(config.windowMs / 1000)
      });
    }

    const remaining = Math.max(0, config.maxRequests - validRequests.length);
    const resetTime = new Date(now + config.windowMs);

    return {
      allowed,
      remaining,
      resetTime,
      totalRequests: validRequests.length,
      message: allowed ? undefined : config.message || 'Rate limit exceeded'
    };
  }

  /**
   * Token bucket rate limiting
   */
  private async tokenBucketCheck(
    key: string,
    config: RateLimitConfig
  ): Promise<RateLimitResult> {
    const bucketKey = `${key}:bucket`;
    const now = Date.now();
    
    // Get or initialize bucket state
    let bucket = await this.cacheService.get<TokenBucketState>(bucketKey);
    
    if (!bucket) {
      bucket = {
        tokens: config.maxRequests,
        lastRefill: now,
        capacity: config.maxRequests,
        refillRate: config.maxRequests / (config.windowMs / 1000) // tokens per second
      };
    }

    // Calculate tokens to add based on time elapsed
    const timePassed = (now - bucket.lastRefill) / 1000; // seconds
    const tokensToAdd = Math.floor(timePassed * bucket.refillRate);
    
    // Refill bucket
    bucket.tokens = Math.min(bucket.capacity, bucket.tokens + tokensToAdd);
    bucket.lastRefill = now;

    // Check if request is allowed
    const allowed = bucket.tokens > 0;
    
    if (allowed) {
      bucket.tokens--;
    }

    // Save updated bucket state
    await this.cacheService.set(bucketKey, bucket, {
      ttl: Math.ceil(config.windowMs / 1000) * 2 // Keep bucket longer than window
    });

    const resetTime = new Date(now + ((bucket.capacity - bucket.tokens) / bucket.refillRate * 1000));

    return {
      allowed,
      remaining: bucket.tokens,
      resetTime,
      totalRequests: bucket.capacity - bucket.tokens,
      message: allowed ? undefined : config.message || 'Rate limit exceeded'
    };
  }

  /**
   * Fixed window rate limiting
   */
  private async fixedWindowCheck(
    key: string,
    config: RateLimitConfig
  ): Promise<RateLimitResult> {
    const now = Date.now();
    const windowStart = Math.floor(now / config.windowMs) * config.windowMs;
    const windowKey = `${key}:${windowStart}`;

    // Get current count for this window
    const currentCount = await this.cacheService.get<number>(windowKey) || 0;
    
    // Check if limit exceeded
    const allowed = currentCount < config.maxRequests;
    
    if (allowed) {
      // Increment counter
      await this.cacheService.set(windowKey, currentCount + 1, {
        ttl: Math.ceil(config.windowMs / 1000)
      });
    }

    const remaining = Math.max(0, config.maxRequests - currentCount - (allowed ? 1 : 0));
    const resetTime = new Date(windowStart + config.windowMs);

    return {
      allowed,
      remaining,
      resetTime,
      totalRequests: currentCount + (allowed ? 1 : 0),
      message: allowed ? undefined : config.message || 'Rate limit exceeded'
    };
  }

  /**
   * Create rate limit middleware
   */
  createMiddleware(config: RateLimitConfig) {
    return async (identifier: string): Promise<RateLimitResult> => {
      return this.checkRateLimit(identifier, config);
    };
  }

  /**
   * Get rate limit status without consuming
   */
  async getRateLimitStatus(
    identifier: string,
    config: RateLimitConfig
  ): Promise<Omit<RateLimitResult, 'allowed'>> {
    const key = config.keyGenerator ? config.keyGenerator(identifier) : `rate_limit:${identifier}`;

    switch (config.strategy) {
      case 'sliding_window': {
        const now = Date.now();
        const windowStart = now - config.windowMs;
        const requestsKey = `${key}:requests`;
        const existingRequests = await this.cacheService.get<number[]>(requestsKey) || [];
        const validRequests = existingRequests.filter(timestamp => timestamp > windowStart);
        
        return {
          remaining: Math.max(0, config.maxRequests - validRequests.length),
          resetTime: new Date(now + config.windowMs),
          totalRequests: validRequests.length
        };
      }

      case 'token_bucket': {
        const bucketKey = `${key}:bucket`;
        const bucket = await this.cacheService.get<TokenBucketState>(bucketKey);
        
        if (!bucket) {
          return {
            remaining: config.maxRequests,
            resetTime: new Date(Date.now() + config.windowMs),
            totalRequests: 0
          };
        }

        const now = Date.now();
        const timePassed = (now - bucket.lastRefill) / 1000;
        const tokensToAdd = Math.floor(timePassed * bucket.refillRate);
        const currentTokens = Math.min(bucket.capacity, bucket.tokens + tokensToAdd);
        
        return {
          remaining: currentTokens,
          resetTime: new Date(now + ((bucket.capacity - currentTokens) / bucket.refillRate * 1000)),
          totalRequests: bucket.capacity - currentTokens
        };
      }

      case 'fixed_window': {
        const now = Date.now();
        const windowStart = Math.floor(now / config.windowMs) * config.windowMs;
        const windowKey = `${key}:${windowStart}`;
        const currentCount = await this.cacheService.get<number>(windowKey) || 0;
        
        return {
          remaining: Math.max(0, config.maxRequests - currentCount),
          resetTime: new Date(windowStart + config.windowMs),
          totalRequests: currentCount
        };
      }

      default:
        throw new Error(`Unknown rate limit strategy: ${config.strategy}`);
    }
  }

  /**
   * Reset rate limit for identifier
   */
  async resetRateLimit(identifier: string, config: RateLimitConfig): Promise<void> {
    const key = config.keyGenerator ? config.keyGenerator(identifier) : `rate_limit:${identifier}`;

    switch (config.strategy) {
      case 'sliding_window':
        await this.cacheService.delete(`${key}:requests`);
        break;
      case 'token_bucket':
        await this.cacheService.delete(`${key}:bucket`);
        break;
      case 'fixed_window':
        // For fixed window, we need to clear all possible window keys
        // This is a simplified approach - in production you might want to track active windows
        const now = Date.now();
        const windowStart = Math.floor(now / config.windowMs) * config.windowMs;
        await this.cacheService.delete(`${key}:${windowStart}`);
        break;
    }
  }

  /**
   * Get all rate limit keys (for debugging/monitoring)
   */
  async getRateLimitKeys(pattern: string = 'rate_limit:*'): Promise<string[]> {
    // This would need to be implemented based on your cache implementation
    // For now, return empty array
    return [];
  }

  /**
   * Cleanup expired rate limit data
   */
  async cleanup(): Promise<void> {
    // This would clean up expired rate limit data
    // Implementation depends on your cache backend
    console.log('Rate limit cleanup completed');
  }
}

// Predefined rate limit configurations
export const RATE_LIMIT_CONFIGS = {
  // API rate limits
  API_GENERAL: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
    strategy: 'sliding_window' as const,
    message: 'Too many requests, please try again later'
  },

  API_STRICT: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 20,
    strategy: 'sliding_window' as const,
    message: 'Rate limit exceeded for this endpoint'
  },

  // Authentication rate limits
  LOGIN_ATTEMPTS: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5,
    strategy: 'fixed_window' as const,
    message: 'Too many login attempts, please try again later'
  },

  PASSWORD_RESET: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3,
    strategy: 'fixed_window' as const,
    message: 'Too many password reset requests'
  },

  // User actions
  ORDER_CREATION: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 2,
    strategy: 'token_bucket' as const,
    message: 'Please wait before creating another order'
  },

  SEARCH_REQUESTS: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 30,
    strategy: 'sliding_window' as const,
    message: 'Too many search requests'
  }
};

// Global instance
export const rateLimitService = RateLimitService.getInstance();
