/**
 * Notification Service
 * Business logic cho Notification management
 */

import { BaseService } from './base.service';
import { Injectable } from '../di-container';
import { NotificationRepository, UserRepository } from '../repositories';
import {
  NotificationEntity,
  CreateNotificationData,
  UpdateNotificationData,
  NotificationBusinessRules
} from '../../models/notification.model';
import { 
  PaginatedResult,
  SearchFilters,
  NotFoundError,
  ValidationError,
  ForbiddenError
} from '../../models/common.model';
import { UserEntity } from '../../models/user.model';

// Service identifier
export const NOTIFICATION_SERVICE = Symbol('NotificationService');

@Injectable
export class NotificationService extends BaseService {
  private notificationRepository: NotificationRepository;
  private userRepository: UserRepository;

  constructor(
    notificationRepository: NotificationRepository,
    userRepository: UserRepository
  ) {
    super();
    this.notificationRepository = notificationRepository;
    this.userRepository = userRepository;
  }

  /**
   * Tạo notification mới
   */
  async createNotification(
    data: CreateNotificationData,
    createdBy?: UserEntity
  ): Promise<NotificationEntity> {
    return this.executeWithErrorHandling(async () => {
      // Validate input
      this.validateRequired(data, ['userId', 'title', 'message', 'type']);

      // Check if user exists
      const user = await this.userRepository.findById(data.userId);
      if (!user) {
        throw new NotFoundError('User', data.userId);
      }

      // Validate notification data
      const validation = NotificationBusinessRules.validateNotification(data);
      if (!validation.valid) {
        throw new ValidationError(validation.errors.join(', '));
      }

      // Create notification
      const notificationData = {
        ...data,
        status: 'UNREAD',
        createdAt: new Date()
      };

      const notification = await this.notificationRepository.create(notificationData) as unknown as NotificationEntity;

      // Log activity
      if (createdBy) {
        await this.logActivity('NOTIFICATION_CREATED', createdBy.id, {
          notificationId: notification.id,
          userId: data.userId,
          type: data.type
        });
      }

      return notification;
    }, 'createNotification');
  }

  /**
   * Lấy notification theo ID
   */
  async getNotificationById(id: string, requestedBy: UserEntity): Promise<NotificationEntity> {
    return this.executeWithErrorHandling(async () => {
      const notification = await this.notificationRepository.findById(id);
      if (!notification) {
        throw new NotFoundError('Notification', id);
      }

      // Check permission
      if (notification.userId !== requestedBy.id && !this.isAdmin(requestedBy)) {
        throw new ForbiddenError('Cannot access notification of another user');
      }

      return notification as unknown as NotificationEntity;
    }, 'getNotificationById');
  }

  /**
   * Cập nhật notification
   */
  async updateNotification(
    id: string,
    data: UpdateNotificationData,
    updatedBy: UserEntity
  ): Promise<NotificationEntity> {
    return this.executeWithErrorHandling(async () => {
      // Check if notification exists
      const existingNotification = await this.notificationRepository.findById(id);
      if (!existingNotification) {
        throw new NotFoundError('Notification', id);
      }

      // Check permission
      if (existingNotification.userId !== updatedBy.id && !this.isAdmin(updatedBy)) {
        throw new ForbiddenError('Cannot update notification of another user');
      }

      // Update notification
      const updatedNotification = await this.notificationRepository.update(id, data) as unknown as NotificationEntity;

      // Log activity
      await this.logActivity('NOTIFICATION_UPDATED', updatedBy.id, {
        notificationId: id,
        changes: Object.keys(data)
      });

      return updatedNotification;
    }, 'updateNotification');
  }

  /**
   * Xóa notification
   */
  async deleteNotification(id: string, deletedBy: UserEntity): Promise<void> {
    return this.executeWithErrorHandling(async () => {
      // Check if notification exists
      const notification = await this.notificationRepository.findById(id);
      if (!notification) {
        throw new NotFoundError('Notification', id);
      }

      // Check permission
      if (notification.userId !== deletedBy.id && !this.isAdmin(deletedBy)) {
        throw new ForbiddenError('Cannot delete notification of another user');
      }

      // Delete notification
      await this.notificationRepository.delete(id);

      // Log activity
      await this.logActivity('NOTIFICATION_DELETED', deletedBy.id, {
        notificationId: id,
        userId: notification.userId
      });
    }, 'deleteNotification');
  }

  /**
   * Lấy notifications của user
   */
  async getUserNotifications(
    userId: string,
    requestedBy: UserEntity,
    filters: SearchFilters & { 
      status?: 'READ' | 'unread';
      type?: string;
    } = {}
  ): Promise<PaginatedResult<NotificationEntity>> {
    return this.executeWithErrorHandling(async () => {
      // Check permission
      if (userId !== requestedBy.id && !this.isAdmin(requestedBy)) {
        throw new ForbiddenError('Cannot access notifications of another user');
      }

      // Validate pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      this.validatePagination(page, limit);

      // Build search conditions
      const searchConditions: any = { userId };

      if (filters.status) {
        searchConditions.status = filters.status.toUpperCase();
      }

      if (filters.type) {
        searchConditions.type = filters.type;
      }

      if (filters.search) {
        const searchQuery = this.sanitizeSearchQuery(filters.search);
        searchConditions.OR = [
          { title: { contains: searchQuery, mode: 'insensitive' } },
          { message: { contains: searchQuery, mode: 'insensitive' } }
        ];
      }

      // Get notifications with pagination
      const result = await this.notificationRepository.findWithPagination({
        page,
        limit,
        where: searchConditions,
        orderBy: {
          [filters.sortBy || 'createdAt']: filters.sortOrder || 'desc'
        }
      });

      return {
        data: result.data.map(notification => notification as unknown as NotificationEntity),
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages
      };
    }, 'getUserNotifications');
  }

  /**
   * Mark notification as read
   */
  async markAsRead(id: string, requestedBy: UserEntity): Promise<NotificationEntity> {
    return this.executeWithErrorHandling(async () => {
      const notification = await this.updateNotification(
        id,
        { 
          status: 'READ',
          readAt: new Date()
        },
        requestedBy
      );

      // Log activity
      await this.logActivity('NOTIFICATION_READ', requestedBy.id, {
        notificationId: id
      });

      return notification;
    }, 'markAsRead');
  }

  /**
   * Mark notification as unread
   */
  async markAsUnread(id: string, requestedBy: UserEntity): Promise<NotificationEntity> {
    return this.executeWithErrorHandling(async () => {
      const notification = await this.updateNotification(
        id,
        { 
          status: 'UNREAD',
          readAt: null
        },
        requestedBy
      );

      // Log activity
      await this.logActivity('NOTIFICATION_UNREAD', requestedBy.id, {
        notificationId: id
      });

      return notification;
    }, 'markAsUnread');
  }

  /**
   * Mark all notifications as read for user
   */
  async markAllAsRead(userId: string, requestedBy: UserEntity): Promise<number> {
    return this.executeWithErrorHandling(async () => {
      // Check permission
      if (userId !== requestedBy.id && !this.isAdmin(requestedBy)) {
        throw new ForbiddenError('Cannot mark notifications for another user');
      }

      const count = await this.notificationRepository.markAllAsRead(userId);

      // Log activity
      await this.logActivity('NOTIFICATIONS_ALL_READ', requestedBy.id, {
        userId,
        count
      });

      return count;
    }, 'markAllAsRead');
  }

  /**
   * Lấy unread notification count
   */
  async getUnreadCount(userId: string, requestedBy: UserEntity): Promise<number> {
    return this.executeWithErrorHandling(async () => {
      // Check permission
      if (userId !== requestedBy.id && !this.isAdmin(requestedBy)) {
        throw new ForbiddenError('Cannot access notification count of another user');
      }

      return await this.notificationRepository.getUnreadCount(userId);
    }, 'getUnreadCount');
  }

  /**
   * Send notification to user
   */
  async sendNotification(
    userId: string,
    title: string,
    message: string,
    type: string,
    data?: any,
    createdBy?: UserEntity
  ): Promise<NotificationEntity> {
    return this.executeWithErrorHandling(async () => {
      const notificationData: CreateNotificationData = {
        userId,
        title,
        message,
        type,
        data
      };

      return await this.createNotification(notificationData, createdBy);
    }, 'sendNotification');
  }

  /**
   * Send bulk notifications
   */
  async sendBulkNotifications(
    userIds: string[],
    title: string,
    message: string,
    type: string,
    data?: any,
    createdBy?: UserEntity
  ): Promise<{
    success: string[];
    failed: { userId: string; reason: string }[];
  }> {
    return this.executeWithErrorHandling(async () => {
      const success: string[] = [];
      const failed: { userId: string; reason: string }[] = [];

      for (const userId of userIds) {
        try {
          await this.sendNotification(userId, title, message, type, data, createdBy);
          success.push(userId);
        } catch (error) {
          failed.push({
            userId,
            reason: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      // Log activity
      if (createdBy) {
        await this.logActivity('NOTIFICATIONS_BULK_SENT', createdBy.id, {
          successCount: success.length,
          failedCount: failed.length,
          type
        });
      }

      return { success, failed };
    }, 'sendBulkNotifications');
  }

  /**
   * Delete old notifications
   */
  async deleteOldNotifications(
    olderThanDays: number = 30,
    requestedBy: UserEntity
  ): Promise<number> {
    return this.executeWithErrorHandling(async () => {
      // Check admin permission
      if (!this.isAdmin(requestedBy)) {
        throw new ForbiddenError('Only admins can delete old notifications');
      }

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      const count = await this.notificationRepository.deleteOldNotifications(cutoffDate);

      // Log activity
      await this.logActivity('NOTIFICATIONS_CLEANUP', requestedBy.id, {
        deletedCount: count,
        olderThanDays
      });

      return count;
    }, 'deleteOldNotifications');
  }

  /**
   * Get notification statistics
   */
  async getNotificationStats(
    userId?: string,
    requestedBy?: UserEntity
  ): Promise<{
    total: number;
    unread: number;
    byType: Record<string, number>;
  }> {
    return this.executeWithErrorHandling(async () => {
      // Check permission for user-specific stats
      if (userId && requestedBy) {
        if (userId !== requestedBy.id && !this.isAdmin(requestedBy)) {
          throw new ForbiddenError('Cannot access notification stats of another user');
        }
      }

      const stats = await this.notificationRepository.getNotificationStats(userId);
      return stats;
    }, 'getNotificationStats');
  }

  /**
   * Helper method to check if user is admin
   */
  private isAdmin(user: UserEntity): boolean {
    return ['ADMIN', 'SUPER_ADMIN'].includes(user.role);
  }
}
