import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../auth/[...nextauth]/route";
import { z } from "zod";
import { container } from "@/app/api/di-container";
import { USER_SERVICE } from "@/app/api/services/service-identifiers";
import {
  NotFoundError,
  ValidationError,
  ForbiddenError,
} from "@/app/models/common.model";

const updateProfileSchema = z.object({
  name: z.string().min(1, "Họ tên là bắt buộc"),
  phone: z.string().optional(),
  dateOfBirth: z.string().optional(),
  gender: z.enum(["MALE", "FEMALE", "OTHER"]).optional(),
});

// GET /api/profile - Lấy thông tin profile của user
export async function GET(_request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Vui lòng đăng nhập" },
        { status: 401 }
      );
    }

    // Get UserService from DI container
    const userService = container.resolve(USER_SERVICE) as any;

    // Get user with relations
    const user = await userService.getUserWithRelations(session.user.id);

    if (!user) {
      return NextResponse.json(
        { error: "Không tìm thấy người dùng" },
        { status: 404 }
      );
    }

    // Remove sensitive data
    const { password: _password, ...userProfile } = user;

    return NextResponse.json(userProfile);
  } catch (error) {
    if (error instanceof NotFoundError) {
      return NextResponse.json(
        { error: "Không tìm thấy người dùng" },
        { status: 404 }
      );
    }

    console.error("Get profile error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi lấy thông tin cá nhân" },
      { status: 500 }
    );
  }
}

// PUT /api/profile - Cập nhật thông tin profile
export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session) {
      return NextResponse.json(
        { error: "Vui lòng đăng nhập" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const data = updateProfileSchema.parse(body);

    // Get UserService from DI container
    const userService = container.resolve(USER_SERVICE) as any;

    // Get current user for permission check
    const currentUser = await userService.getUserById(session.user.id);

    // Prepare update data
    const updateData: any = {
      name: data.name,
    };

    if (data.phone) {
      updateData.phone = data.phone;
    }

    if (data.dateOfBirth) {
      updateData.dateOfBirth = new Date(data.dateOfBirth);
    }

    if (data.gender) {
      updateData.gender = data.gender;
    }

    // Update user using service
    await userService.updateUser(session.user.id, updateData, currentUser);

    // Get user with relations for response
    const userWithRelations = await userService.getUserWithRelations(
      session.user.id
    );

    // Remove sensitive data
    const { password: _password, ...userProfile } = userWithRelations;

    return NextResponse.json(userProfile);
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: error.errors[0].message },
        { status: 400 }
      );
    }

    if (error instanceof ValidationError) {
      return NextResponse.json({ error: error.message }, { status: 400 });
    }

    if (error instanceof NotFoundError) {
      return NextResponse.json(
        { error: "Không tìm thấy người dùng" },
        { status: 404 }
      );
    }

    if (error instanceof ForbiddenError) {
      return NextResponse.json(
        { error: "Không có quyền cập nhật thông tin này" },
        { status: 403 }
      );
    }

    console.error("Update profile error:", error);
    return NextResponse.json(
      { error: "Có lỗi xảy ra khi cập nhật thông tin cá nhân" },
      { status: 500 }
    );
  }
}
