/**
 * Authentication utilities for API routes
 */

import { NextRequest } from "next/server";
import { verify } from "jsonwebtoken";
import { prisma } from "@/lib/prisma";

export interface AuthResult {
  success: boolean;
  user?: any;
  error?: string;
}

export interface AdminAuthResult {
  success: boolean;
  admin?: any;
  error?: string;
}

/**
 * Verify user authentication
 */
export async function verifyAuth(request: NextRequest): Promise<AuthResult> {
  try {
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return {
        success: false,
        error: "Missing or invalid authorization header",
      };
    }

    const token = authHeader.substring(7);
    if (!token) {
      return {
        success: false,
        error: "Missing token",
      };
    }

    // Verify JWT token
    const secret = process.env.JWT_SECRET || "your-secret-key";
    const decoded = verify(token, secret) as any;

    if (!decoded.userId) {
      return {
        success: false,
        error: "Invalid token payload",
      };
    }

    // Get user from database
    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      select: {
        id: true,
        email: true,
        name: true,
        isActive: true,
      },
    });

    if (!user) {
      return {
        success: false,
        error: "User not found",
      };
    }

    if (!user.isActive) {
      return {
        success: false,
        error: "User account is inactive",
      };
    }

    return {
      success: true,
      user,
    };
  } catch (error) {
    console.error("Auth verification error:", error);
    return {
      success: false,
      error: "Invalid token",
    };
  }
}

/**
 * Verify admin authentication
 */
export async function verifyAdminAuth(
  request: NextRequest
): Promise<AdminAuthResult> {
  try {
    const authHeader = request.headers.get("authorization");
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return {
        success: false,
        error: "Missing or invalid authorization header",
      };
    }

    const token = authHeader.substring(7);
    if (!token) {
      return {
        success: false,
        error: "Missing token",
      };
    }

    // Verify JWT token
    const secret = process.env.JWT_SECRET || "your-secret-key";
    const decoded = verify(token, secret) as any;

    if (!decoded.adminId) {
      return {
        success: false,
        error: "Invalid token payload",
      };
    }

    // Get admin from database
    const admin = await prisma.adminUser.findUnique({
      where: { id: decoded.adminId },
      select: {
        id: true,
        email: true,
        name: true,
        role: true,
        isActive: true,
        permissions: true,
      },
    });

    if (!admin) {
      return {
        success: false,
        error: "Admin not found",
      };
    }

    if (!admin.isActive) {
      return {
        success: false,
        error: "Admin account is inactive",
      };
    }

    return {
      success: true,
      admin,
    };
  } catch (error) {
    console.error("Admin auth verification error:", error);
    return {
      success: false,
      error: "Invalid token",
    };
  }
}

/**
 * Check if admin has specific permission
 */
export function hasPermission(admin: any, permission: string): boolean {
  if (!admin || !admin.permissions) return false;

  // Super admin has all permissions
  if (admin.role === "SUPER_ADMIN") return true;

  // Check if admin has the specific permission or wildcard
  return (
    admin.permissions.includes(permission) || admin.permissions.includes("*")
  );
}

/**
 * Require specific permission
 */
export async function requirePermission(
  request: NextRequest,
  permission: string
): Promise<AdminAuthResult> {
  const authResult = await verifyAdminAuth(request);

  if (!authResult.success) {
    return authResult;
  }

  if (!hasPermission(authResult.admin, permission)) {
    return {
      success: false,
      error: "Insufficient permissions",
    };
  }

  return authResult;
}

/**
 * Extract user ID from request
 */
export async function getUserId(request: NextRequest): Promise<string | null> {
  const authResult = await verifyAuth(request);
  return authResult.success ? authResult.user?.id : null;
}

/**
 * Extract admin ID from request
 */
export async function getAdminId(request: NextRequest): Promise<string | null> {
  const authResult = await verifyAdminAuth(request);
  return authResult.success ? authResult.admin?.id : null;
}

/**
 * Get client IP address
 */
export function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get("x-forwarded-for");
  const realIP = request.headers.get("x-real-ip");

  if (forwarded) {
    return forwarded.split(",")[0].trim();
  }

  if (realIP) {
    return realIP;
  }

  return "unknown";
}
