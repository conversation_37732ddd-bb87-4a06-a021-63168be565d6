"use client";

import React, { createContext, useContext, useState, useCallback } from "react";

// Types for admin state management
export interface PaginationParams {
  page: number;
  limit: number;
  total?: number;
  totalPages?: number;
  hasNext?: boolean;
  hasPrev?: boolean;
}

export interface SearchFilters {
  search?: string;
  status?: string;
  category?: string;
  brand?: string;
  role?: string;
  featured?: boolean;
  [key: string]: any;
}

export interface AdminState<T = any> {
  data: T[];
  loading: boolean;
  error: string | null;
  pagination: PaginationParams;
  filters: SearchFilters;
}

interface AdminContextType {
  // Generic state management
  states: Map<string, AdminState>;
  getState: (key: string) => AdminState | undefined;
  setState: (key: string, state: Partial<AdminState>) => void;
  resetState: (key: string) => void;

  // Loading states for specific operations
  operationLoading: Map<string, boolean>;
  setOperationLoading: (operation: string, loading: boolean) => void;
  isOperationLoading: (operation: string) => boolean;

  // Global admin actions
  refreshData: (key: string) => void;
}

const AdminContext = createContext<AdminContextType | undefined>(undefined);

const createInitialState = (): AdminState => ({
  data: [],
  loading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  },
  filters: {},
});

export function AdminProvider({ children }: { children: React.ReactNode }) {
  const [states, setStates] = useState<Map<string, AdminState>>(new Map());
  const [operationLoading, setOperationLoadingState] = useState<
    Map<string, boolean>
  >(new Map());

  const getState = useCallback(
    (key: string) => {
      if (!states.has(key)) {
        const newState = createInitialState();
        setStates((prev) => new Map(prev).set(key, newState));
        return newState;
      }
      return states.get(key);
    },
    [states]
  );

  const setState = useCallback((key: string, newState: Partial<AdminState>) => {
    setStates((prev) => {
      const currentState = prev.get(key) || createInitialState();
      const updatedState = { ...currentState, ...newState };
      return new Map(prev).set(key, updatedState);
    });
  }, []);

  const resetState = useCallback((key: string) => {
    setStates((prev) => {
      const newMap = new Map(prev);
      newMap.set(key, createInitialState());
      return newMap;
    });
  }, []);

  const setOperationLoading = useCallback(
    (operation: string, loading: boolean) => {
      setOperationLoadingState((prev) => new Map(prev).set(operation, loading));
    },
    []
  );

  const isOperationLoading = useCallback(
    (operation: string) => {
      return operationLoading.get(operation) || false;
    },
    [operationLoading]
  );

  const refreshData = useCallback(
    (key: string) => {
      // Trigger refresh by setting loading to true
      setState(key, { loading: true, error: null });
    },
    [setState]
  );

  const value: AdminContextType = {
    states,
    getState,
    setState,
    resetState,
    operationLoading,
    setOperationLoading,
    isOperationLoading,
    refreshData,
  };

  return (
    <AdminContext.Provider value={value}>{children}</AdminContext.Provider>
  );
}

export function useAdmin() {
  const context = useContext(AdminContext);
  if (context === undefined) {
    throw new Error("useAdmin must be used within an AdminProvider");
  }
  return context;
}

// Helper hook for specific entity state management
export function useAdminState(key: string) {
  const { getState, setState, resetState, refreshData } = useAdmin();

  const state = getState(key) || createInitialState();

  const updateState = useCallback(
    (newState: Partial<AdminState>) => {
      setState(key, newState);
    },
    [key, setState]
  );

  const updateData = useCallback(
    (data: any[]) => {
      setState(key, { data, loading: false, error: null });
    },
    [key, setState]
  );

  const updatePagination = useCallback(
    (pagination: Partial<PaginationParams>) => {
      setState(key, {
        pagination: { ...state.pagination, ...pagination },
      });
    },
    [key, setState, state.pagination]
  );

  const updateFilters = useCallback(
    (filters: Partial<SearchFilters>) => {
      setState(key, {
        filters: { ...state.filters, ...filters },
      });
    },
    [key, setState, state.filters]
  );

  const setLoading = useCallback(
    (loading: boolean) => {
      setState(key, { loading });
    },
    [key, setState]
  );

  const setError = useCallback(
    (error: string | null) => {
      setState(key, { error, loading: false });
    },
    [key, setState]
  );

  const reset = useCallback(() => {
    resetState(key);
  }, [key, resetState]);

  const refresh = useCallback(() => {
    refreshData(key);
  }, [key, refreshData]);

  return {
    ...state,
    updateState,
    updateData,
    updatePagination,
    updateFilters,
    setLoading,
    setError,
    reset,
    refresh,
  };
}
