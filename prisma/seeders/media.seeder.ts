/**
 * Media Seeder
 * Creates media records for brands, categories, and products
 */

import { BaseSeeder } from './lib/base-seeder';

export class MediaSeeder extends BaseSeeder {
  getName(): string {
    return 'Media';
  }

  async seed(): Promise<any[]> {
    this.logStart();

    const mediaRecords = [];

    // Brand logos
    const brandLogos = [
      { name: 'zara-logo.jpg', folder: 'brands' },
      { name: 'h&m-logo.jpg', folder: 'brands' },
      { name: 'uniqlo-logo.jpg', folder: 'brands' },
      { name: 'nike-logo.jpg', folder: 'brands' },
      { name: 'adidas-logo.jpg', folder: 'brands' },
    ];

    for (const logo of brandLogos) {
      const media = await this.createPicsumMediaRecord({
        filename: logo.name,
        folder: logo.folder,
        category: 'brands',
        alt: `${logo.name} logo`,
        title: `${logo.name} logo`,
        width: 200,
        height: 200,
      });
      mediaRecords.push(media);
    }

    // Category images
    const categories = [
      'ao-thun', 'vay-dam', 'quan-jeans', 'ao-khoac', 'phu-kien', 'giay-dep'
    ];

    for (const category of categories) {
      // Main category image
      const mainImage = await this.createPicsumMediaRecord({
        filename: `${category}-main.jpg`,
        folder: 'categories',
        category: category,
        alt: `${category} category`,
        title: `${category} category`,
        width: 400,
        height: 300,
      });
      mediaRecords.push(mainImage);

      // Banner image
      const bannerImage = await this.createPicsumMediaRecord({
        filename: `${category}-banner.jpg`,
        folder: 'categories',
        category: category,
        alt: `${category} banner`,
        title: `${category} banner`,
        width: 800,
        height: 400,
      });
      mediaRecords.push(bannerImage);
    }

    // Product images (will be used by product seeder)
    const productImageCount = 80; // Generate 80 product images
    const fashionCategories = ['fashion', 'ao-thun', 'vay-dam', 'quan-jeans', 'ao-khoac', 'phu-kien', 'giay-dep'];
    
    for (let i = 1; i <= productImageCount; i++) {
      const randomCategory = this.dataGenerator.randomChoice(fashionCategories) as string;
      const media = await this.createPicsumMediaRecord({
        filename: `product-${i}.jpg`,
        folder: 'products',
        category: randomCategory,
        alt: `Product image ${i}`,
        title: `Product image ${i}`,
      });
      mediaRecords.push(media);
      
      // Progress indicator
      if (i % 10 === 0) {
        console.log(`   └── Created ${i}/${productImageCount} product images...`);
      }
    }

    this.logSuccess(mediaRecords.length, 'media records');
    return mediaRecords;
  }
}
