/**
 * Main Seeder Entry Point
 * Orchestrates all seeding operations
 */

import { PrismaClient } from "@prisma/client";
import { AdminUserSeeder } from "./admin-user.seeder";
import { SettingsSeeder } from "./settings.seeder";
import { MediaSeeder } from "./media.seeder";
import { BrandsSeeder } from "./brands.seeder";
import { CategoriesSeeder } from "./categories.seeder";
import { AttributesSeeder } from "./attributes.seeder";
import { UsersSeeder } from "./users.seeder";
import { ProductsSeeder } from "./products.seeder";
import { ContactInfoSeeder } from "./contact-info.seeder";
import { MenuSeeder } from "./menu.seeder";
import { EventsSeeder } from "./events.seeder";
import { PromotionsSeeder } from "./promotions.seeder";
import { ShippingSeeder } from "./shipping.seeder";
import { EmailSMTPSeeder } from "./email-smtp.seeder";
import { SEOSeeder } from "./seo.seeder";

const prisma = new PrismaClient();

async function main() {
  console.log("🌱 Starting NS Shop Database Seeding...\n");

  try {
    // Phase 1: Core System Data
    console.log("📋 Phase 1: Core System Data");
    console.log("================================");

    const adminUserSeeder = new AdminUserSeeder(prisma);
    await adminUserSeeder.seed();

    const settingsSeeder = new SettingsSeeder(prisma);
    await settingsSeeder.seed();

    console.log("");

    // Phase 2: Media Assets
    console.log("🖼️  Phase 2: Media Assets");
    console.log("================================");

    const mediaSeeder = new MediaSeeder(prisma);
    await mediaSeeder.seed();

    console.log("");

    // Phase 3: Product Catalog Foundation
    console.log("🏗️  Phase 3: Product Catalog Foundation");
    console.log("================================");

    const brandsSeeder = new BrandsSeeder(prisma);
    await brandsSeeder.seed();

    const categoriesSeeder = new CategoriesSeeder(prisma);
    await categoriesSeeder.seed();

    const attributesSeeder = new AttributesSeeder(prisma);
    await attributesSeeder.seed();

    console.log("");

    // Phase 4: Users and Customers
    console.log("👥 Phase 4: Users and Customers");
    console.log("================================");

    const usersSeeder = new UsersSeeder(prisma);
    await usersSeeder.seed();

    console.log("");

    // Phase 5: Products and Inventory
    console.log("🛍️  Phase 5: Products and Inventory");
    console.log("================================");

    const productsSeeder = new ProductsSeeder(prisma);
    await productsSeeder.seed();

    console.log("");

    // Phase 6: Business Operations
    console.log("🏪 Phase 6: Business Operations");
    console.log("================================");

    const contactInfoSeeder = new ContactInfoSeeder(prisma);
    await contactInfoSeeder.seed();

    const menuSeeder = new MenuSeeder(prisma);
    await menuSeeder.seed();

    const shippingSeeder = new ShippingSeeder(prisma);
    await shippingSeeder.seed();

    console.log("");

    // Phase 7: Marketing & Communications
    console.log("📢 Phase 7: Marketing & Communications");
    console.log("================================");

    const eventsSeeder = new EventsSeeder(prisma);
    await eventsSeeder.seed();

    const promotionsSeeder = new PromotionsSeeder(prisma);
    await promotionsSeeder.seed();

    const emailSMTPSeeder = new EmailSMTPSeeder(prisma);
    await emailSMTPSeeder.seed();

    console.log("");

    // Phase 8: SEO & Optimization
    console.log("🔍 Phase 8: SEO & Optimization");
    console.log("================================");

    const seoSeeder = new SEOSeeder(prisma);
    await seoSeeder.seed();

    console.log("");

    // Summary
    console.log("✅ SEEDING COMPLETED SUCCESSFULLY!");
    console.log("================================");

    // Show summary statistics
    const stats = await getDatabaseStats();
    console.log("📊 Database Statistics:");
    console.log("\n🏗️  Core System:");
    console.log(`   • Admin Users: ${stats.adminUsers}`);
    console.log(`   • Settings: ${stats.settings}`);
    console.log(`   • Media: ${stats.media}`);
    
    console.log("\n🛍️  Catalog:");
    console.log(`   • Brands: ${stats.brands}`);
    console.log(`   • Categories: ${stats.categories}`);
    console.log(`   • Attributes: ${stats.attributes}`);
    console.log(`   • Attribute Values: ${stats.attributeValues}`);
    console.log(`   • Products: ${stats.products}`);
    console.log(`   • Product Attributes: ${stats.productAttributes}`);
    console.log(`   • Product Media: ${stats.productMedia}`);
    console.log(`   • Inventory Entries: ${stats.inventoryEntries}`);
    
    console.log("\n👥 Users:");
    console.log(`   • Users: ${stats.users}`);
    console.log(`   • Addresses: ${stats.addresses}`);
    
    console.log("\n🏪 Business:");
    console.log(`   • Contact Info: ${stats.contactInfo}`);
    console.log(`   • Menus: ${stats.menus}`);
    console.log(`   • Menu Items: ${stats.menuItems}`);
    console.log(`   • Shipping Zones: ${stats.shippingZones}`);
    console.log(`   • Shipping Methods: ${stats.shippingMethods}`);
    
    console.log("\n📢 Marketing:");
    console.log(`   • Events: ${stats.events}`);
    console.log(`   • Promotions: ${stats.promotions}`);
    console.log(`   • Email Templates: ${stats.emailTemplates}`);
    console.log(`   • SMTP Configs: ${stats.smtpConfigs}`);
    
    console.log("\n🔍 SEO:");
    console.log(`   • SEO Settings: ${stats.seoSettings}`);
    console.log(`   • Page SEO: ${stats.pageSEO}`);

    console.log("\n🎉 DATABASE SEEDING COMPLETED!");
    console.log("💡 Your e-commerce platform is now ready with:");
    console.log("   ✅ Complete product catalog with images");
    console.log("   ✅ User management system");
    console.log("   ✅ Business operations setup");
    console.log("   ✅ Marketing & promotion tools");
    console.log("   ✅ SEO optimization");
    console.log("   ✅ Email & communication system");
  } catch (error) {
    console.error("❌ Seeding failed:", error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

async function getDatabaseStats() {
  const [
    adminUsers,
    settings,
    media,
    brands,
    categories,
    attributes,
    attributeValues,
    users,
    addresses,
    products,
    productAttributes,
    productMedia,
    inventoryEntries,
    contactInfo,
    menus,
    menuItems,
    events,
    promotions,
    shippingZones,
    shippingMethods,
    emailTemplates,
    smtpConfigs,
    seoSettings,
    pageSEO,
  ] = await Promise.all([
    prisma.adminUser.count(),
    prisma.setting.count(),
    prisma.media.count(),
    prisma.brand.count(),
    prisma.category.count(),
    prisma.attribute.count(),
    prisma.attributeValue.count(),
    prisma.user.count(),
    prisma.address.count(),
    prisma.product.count(),
    prisma.productAttribute.count(),
    prisma.productMedia.count(),
    prisma.inventoryEntry.count(),
    prisma.contactInfo.count(),
    prisma.menu.count(),
    prisma.menuItem.count(),
    prisma.event.count(),
    prisma.promotion.count(),
    prisma.shippingZone.count(),
    prisma.shippingMethod.count(),
    prisma.emailTemplate.count(),
    prisma.sMTPConfig.count(),
    prisma.sEOSettings.count(),
    prisma.pageSEO.count(),
  ]);

  return {
    adminUsers,
    settings,
    media,
    brands,
    categories,
    attributes,
    attributeValues,
    users,
    addresses,
    products,
    productAttributes,
    productMedia,
    inventoryEntries,
    contactInfo,
    menus,
    menuItems,
    events,
    promotions,
    shippingZones,
    shippingMethods,
    emailTemplates,
    smtpConfigs,
    seoSettings,
    pageSEO,
  };
}

// Handle unhandled promise rejections
process.on("unhandledRejection", (err) => {
  console.error("Unhandled Promise Rejection:", err);
  process.exit(1);
});

// Run the seeder
main();
