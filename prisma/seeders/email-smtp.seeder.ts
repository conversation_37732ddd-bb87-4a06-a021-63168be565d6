/**
 * Email & SMTP Seeder
 * Creates email templates and SMTP configurations
 */

import { BaseSeeder } from "./lib/base-seeder";

export class EmailSMTPSeeder extends BaseSeeder {
  getName(): string {
    return "EmailSMTP";
  }

  async seed(): Promise<any[]> {
    this.logStart();

    // Create SMTP configurations
    const smptConfigsData = [
      {
        name: "Gmail SMTP",
        host: "smtp.gmail.com",
        port: 587,
        secure: true,
        username: "<EMAIL>",
        password: "encrypted_password_here", // Should be encrypted in real app
        fromName: "NS Shop",
        fromEmail: "<EMAIL>",
        isActive: true,
        isDefault: true,
      },
      {
        name: "SendGrid SMTP",
        host: "smtp.sendgrid.net",
        port: 587,
        secure: true,
        username: "apikey",
        password: "encrypted_sendgrid_key_here",
        fromName: "NS Shop",
        fromEmail: "<EMAIL>",
        isActive: false,
        isDefault: false,
      }
    ];

    const createdSMTPConfigs = [];
    for (const configData of smptConfigsData) {
      // Check if SMTP config already exists
      const existing = await this.prisma.sMTPConfig.findFirst({
        where: { name: configData.name }
      });

      let config;
      if (existing) {
        config = await this.prisma.sMTPConfig.update({
          where: { id: existing.id },
          data: configData
        });
      } else {
        config = await this.prisma.sMTPConfig.create({
          data: configData
        });
      }
      createdSMTPConfigs.push(config);
    }

    // Create email templates
    const emailTemplatesData = [
      {
        name: "Welcome Email",
        subject: "Chào mừng bạn đến với NS Shop!",
        content: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <title>Chào mừng đến NS Shop</title>
          </head>
          <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
              <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #e74c3c;">NS Shop</h1>
                <h2>Chào mừng {{customer_name}}!</h2>
              </div>
              
              <p>Cảm ơn bạn đã đăng ký tài khoản tại NS Shop. Chúng tôi rất vui mừng chào đón bạn vào cộng đồng thời trang của chúng tôi.</p>
              
              <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                <h3>Thông tin tài khoản:</h3>
                <p><strong>Email:</strong> {{customer_email}}</p>
                <p><strong>Ngày đăng ký:</strong> {{registration_date}}</p>
              </div>
              
              <p>Để bắt đầu mua sắm, hãy khám phá bộ sưu tập thời trang mới nhất của chúng tôi:</p>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="{{shop_url}}/products" style="background: #e74c3c; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Khám phá sản phẩm</a>
              </div>
              
              <p>Nếu bạn có bất kỳ câu hỏi nào, đừng ngần ngại liên hệ với chúng tôi qua email hoặc hotline 1900-xxxx.</p>
              
              <div style="border-top: 1px solid #eee; margin-top: 30px; padding-top: 20px; text-align: center; color: #666;">
                <p>Trân trọng,<br>Đội ngũ NS Shop</p>
                <p><small>© 2024 NS Shop. All rights reserved.</small></p>
              </div>
            </div>
          </body>
          </html>
        `,
        type: "WELCOME",
        variables: {
          customer_name: "Tên khách hàng",
          customer_email: "Email khách hàng", 
          registration_date: "Ngày đăng ký",
          shop_url: "URL website"
        },
        isActive: true,
        isDefault: true,
        createdBy: "system",
      },
      {
        name: "Order Confirmation",
        subject: "Xác nhận đơn hàng #{{order_number}} - NS Shop",
        content: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <title>Xác nhận đơn hàng</title>
          </head>
          <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
              <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #e74c3c;">NS Shop</h1>
                <h2>Xác nhận đơn hàng</h2>
              </div>
              
              <p>Xin chào {{customer_name}},</p>
              <p>Cảm ơn bạn đã đặt hàng tại NS Shop. Đơn hàng của bạn đã được xác nhận và đang được xử lý.</p>
              
              <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                <h3>Thông tin đơn hàng:</h3>
                <table style="width: 100%; border-collapse: collapse;">
                  <tr>
                    <td><strong>Mã đơn hàng:</strong></td>
                    <td>{{order_number}}</td>
                  </tr>
                  <tr>
                    <td><strong>Ngày đặt:</strong></td>
                    <td>{{order_date}}</td>
                  </tr>
                  <tr>
                    <td><strong>Tổng tiền:</strong></td>
                    <td style="color: #e74c3c; font-weight: bold;">{{order_total}}</td>
                  </tr>
                  <tr>
                    <td><strong>Phương thức thanh toán:</strong></td>
                    <td>{{payment_method}}</td>
                  </tr>
                </table>
              </div>
              
              <div style="background: #fff; border: 1px solid #ddd; padding: 20px; border-radius: 5px; margin: 20px 0;">
                <h3>Địa chỉ giao hàng:</h3>
                <p>{{shipping_address}}</p>
              </div>
              
              <p>Đơn hàng của bạn sẽ được giao trong vòng {{estimated_delivery}} ngày làm việc.</p>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="{{shop_url}}/orders/{{order_number}}" style="background: #e74c3c; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Theo dõi đơn hàng</a>
              </div>
              
              <div style="border-top: 1px solid #eee; margin-top: 30px; padding-top: 20px; text-align: center; color: #666;">
                <p>Trân trọng,<br>Đội ngũ NS Shop</p>
              </div>
            </div>
          </body>
          </html>
        `,
        type: "ORDER_CONFIRMATION",
        variables: {
          customer_name: "Tên khách hàng",
          order_number: "Mã đơn hàng",
          order_date: "Ngày đặt hàng",
          order_total: "Tổng tiền",
          payment_method: "Phương thức thanh toán",
          shipping_address: "Địa chỉ giao hàng",
          estimated_delivery: "Thời gian giao hàng dự kiến",
          shop_url: "URL website"
        },
        isActive: true,
        isDefault: true,
        createdBy: "system",
      },
      {
        name: "Order Shipped",
        subject: "Đơn hàng #{{order_number}} đã được giao cho đối tác vận chuyển",
        content: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <title>Đơn hàng đã giao vận chuyển</title>
          </head>
          <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
              <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #e74c3c;">NS Shop</h1>
                <h2>Đơn hàng đã được giao vận chuyển</h2>
              </div>
              
              <p>Xin chào {{customer_name}},</p>
              <p>Đơn hàng #{{order_number}} của bạn đã được giao cho đối tác vận chuyển và đang trên đường đến địa chỉ của bạn.</p>
              
              <div style="background: #e8f5e8; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #28a745;">
                <h3 style="color: #28a745; margin-top: 0;">Thông tin vận chuyển:</h3>
                <table style="width: 100%; border-collapse: collapse;">
                  <tr>
                    <td><strong>Mã vận đơn:</strong></td>
                    <td>{{tracking_number}}</td>
                  </tr>
                  <tr>
                    <td><strong>Đối tác vận chuyển:</strong></td>
                    <td>{{shipping_carrier}}</td>
                  </tr>
                  <tr>
                    <td><strong>Thời gian dự kiến:</strong></td>
                    <td>{{estimated_delivery}}</td>
                  </tr>
                </table>
              </div>
              
              <p>Bạn có thể theo dõi tiến trình giao hàng qua mã vận đơn hoặc trực tiếp trên website của chúng tôi.</p>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="{{tracking_url}}" style="background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin-right: 10px;">Theo dõi vận đơn</a>
                <a href="{{shop_url}}/orders/{{order_number}}" style="background: #17a2b8; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Xem đơn hàng</a>
              </div>
              
              <div style="border-top: 1px solid #eee; margin-top: 30px; padding-top: 20px; text-align: center; color: #666;">
                <p>Trân trọng,<br>Đội ngũ NS Shop</p>
              </div>
            </div>
          </body>
          </html>
        `,
        type: "ORDER_SHIPPED",
        variables: {
          customer_name: "Tên khách hàng",
          order_number: "Mã đơn hàng",
          tracking_number: "Mã vận đơn",
          shipping_carrier: "Đối tác vận chuyển",
          estimated_delivery: "Thời gian giao hàng dự kiến",
          tracking_url: "URL theo dõi vận đơn",
          shop_url: "URL website"
        },
        isActive: true,
        isDefault: true,
        createdBy: "system",
      },
      {
        name: "Password Reset",
        subject: "Yêu cầu đặt lại mật khẩu - NS Shop",
        content: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <title>Đặt lại mật khẩu</title>
          </head>
          <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
              <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #e74c3c;">NS Shop</h1>
                <h2>Đặt lại mật khẩu</h2>
              </div>
              
              <p>Xin chào {{customer_name}},</p>
              <p>Chúng tôi nhận được yêu cầu đặt lại mật khẩu cho tài khoản của bạn.</p>
              
              <div style="background: #fff3cd; padding: 20px; border-radius: 5px; margin: 20px 0; border-left: 4px solid #ffc107;">
                <p><strong>Lưu ý:</strong> Nếu bạn không yêu cầu đặt lại mật khẩu, vui lòng bỏ qua email này. Mật khẩu của bạn sẽ không thay đổi.</p>
              </div>
              
              <p>Để đặt lại mật khẩu, vui lòng nhấp vào nút bên dưới:</p>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="{{reset_url}}" style="background: #e74c3c; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Đặt lại mật khẩu</a>
              </div>
              
              <p>Liên kết này sẽ hết hạn sau {{expiry_hours}} giờ vì lý do bảo mật.</p>
              
              <p>Nếu bạn không thể nhấp vào nút trên, hãy sao chép và dán liên kết sau vào trình duyệt:</p>
              <p style="word-break: break-all; background: #f8f9fa; padding: 10px; border-radius: 3px;">{{reset_url}}</p>
              
              <div style="border-top: 1px solid #eee; margin-top: 30px; padding-top: 20px; text-align: center; color: #666;">
                <p>Trân trọng,<br>Đội ngũ NS Shop</p>
              </div>
            </div>
          </body>
          </html>
        `,
        type: "PASSWORD_RESET",
        variables: {
          customer_name: "Tên khách hàng",
          reset_url: "URL đặt lại mật khẩu",
          expiry_hours: "Số giờ hết hạn"
        },
        isActive: true,
        isDefault: true,
        createdBy: "system",
      },
      {
        name: "Newsletter Template",
        subject: "{{newsletter_title}} - NS Shop Newsletter",
        content: `
          <!DOCTYPE html>
          <html>
          <head>
            <meta charset="utf-8">
            <title>NS Shop Newsletter</title>
          </head>
          <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
              <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #e74c3c;">NS Shop</h1>
                <h2>{{newsletter_title}}</h2>
              </div>
              
              <div style="margin-bottom: 30px;">
                {{newsletter_content}}
              </div>
              
              <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                <h3>Sản phẩm nổi bật tuần này:</h3>
                {{featured_products}}
              </div>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="{{shop_url}}" style="background: #e74c3c; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block;">Khám phá ngay</a>
              </div>
              
              <div style="border-top: 1px solid #eee; margin-top: 30px; padding-top: 20px; text-align: center; color: #666;">
                <p>Bạn nhận email này vì đã đăng ký nhận tin từ NS Shop.</p>
                <p><a href="{{unsubscribe_url}}" style="color: #666;">Hủy đăng ký</a></p>
                <p><small>© 2024 NS Shop. All rights reserved.</small></p>
              </div>
            </div>
          </body>
          </html>
        `,
        type: "NEWSLETTER",
        variables: {
          newsletter_title: "Tiêu đề newsletter",
          newsletter_content: "Nội dung newsletter",
          featured_products: "Danh sách sản phẩm nổi bật",
          shop_url: "URL website",
          unsubscribe_url: "URL hủy đăng ký"
        },
        isActive: true,
        isDefault: true,
        createdBy: "system",
      }
    ];

    const createdTemplates = [];
    for (const templateData of emailTemplatesData) {
      // Check if email template already exists
      const existing = await this.prisma.emailTemplate.findFirst({
        where: { name: templateData.name }
      });

      let template;
      if (existing) {
        template = await this.prisma.emailTemplate.update({
          where: { id: existing.id },
          data: templateData
        });
      } else {
        template = await this.prisma.emailTemplate.create({
          data: templateData
        });
      }
      createdTemplates.push(template);
    }

    this.logSuccess(createdSMTPConfigs.length, "SMTP configurations");
    console.log(`   └── Created ${createdTemplates.length} email templates`);

    // Show template types summary
    const templateTypes = createdTemplates.reduce((acc: any, template) => {
      acc[template.type] = (acc[template.type] || 0) + 1;
      return acc;
    }, {});

    Object.entries(templateTypes).forEach(([type, count]) => {
      console.log(`   └── ${type}: ${count} template(s)`);
    });

    return [...createdSMTPConfigs, ...createdTemplates];
  }
}