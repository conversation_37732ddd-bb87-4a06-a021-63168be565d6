---
type: "always_apply"
---

- <PERSON><PERSON> tạo doc luôn lưu vào thư mục docs.
- <PERSON><PERSON> bạn cần test trên website, hãy test trên http://localhost:6002 mà không cần phải run project bằng command.
- <PERSON><PERSON> xong task phải chạy npx tsc và fix errors.
- <PERSON><PERSON> tạo một bảng mới hay sửa schema, luôn luôn cập nhật lại repositories, services, models, dto liên quan.
- Luôn luôn đảm bảo type-safe trong toàn bộ project. Types/models/entities/dto luôn lưu vào src/app/models hoặc src/app/dto.
- Ưu tiên quản lý state thông qua context/hooks.

## Kiến trúc dự án NS Shop

### 1. Kiến trúc tổng quan
- **Next.js 15** với App Router
- **Prisma ORM** với PostgreSQL database
- **Dependency Injection** pattern với custom DI Container
- **Repository Pattern** với base repository và specific repositories
- **Service Layer** với business logic separation
- **DTO Pattern** với Zod validation
- **Admin panel** tách biệt với user interface

### 2. Cấu trúc thư mục chính
```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API routes với DI pattern
│   ├── admin/             # Admin panel pages
│   ├── models/            # Data models và entities
│   ├── dto/               # Data Transfer Objects
│   └── (các pages khác)/
├── components/            # React components
│   ├── admin/            # Admin-specific components
│   ├── shop/             # Shop-specific components
│   └── ui/               # Reusable UI components
├── contexts/             # React contexts
├── hooks/                # Custom hooks
└── lib/                  # Utilities và services
```

### 3. Flow patterns quan trọng

#### 3.1 API Flow
```
Request → API Route → DI Container → Service → Repository → Database
Response ← DTO Transform ← Service Response ← Repository Response
```

#### 3.2 Repository Pattern
- Mọi repository extend từ `BaseRepository`
- Sử dụng Prisma client thông qua base class
- Implement common CRUD operations
- Pagination và search được built-in

#### 3.3 Service Layer
- Business logic nằm trong services
- Services inject repositories thông qua DI
- Transform data giữa layers
- Handle business rules và validation

#### 3.4 Dependency Injection
- Sử dụng custom DI container (`di-container.ts`)
- Services và repositories được register trong `di-setup.ts`
- Resolve dependencies tự động
- Singleton pattern cho shared instances

### 4. Quy tắc code structure

#### 4.1 Models và DTOs
- **Models** (`src/app/models/`): Database entities và domain objects
- **DTOs** (`src/app/dto/`): API request/response objects với Zod validation
- **Transforms** (`src/app/dto/transforms/`): Convert giữa models và DTOs

#### 4.2 API Routes
- Mọi API route phải sử dụng DI container
- Resolve services từ container, không tạo instance trực tiếp
- Consistent error handling và response format
- Separate admin APIs (`/api/admin/`) và public APIs

#### 4.3 Components Structure
- **Admin components** trong `components/admin/`
- **Shop components** trong `components/shop/`
- **Shared UI** trong `components/ui/`
- Follow compound component pattern khi cần thiết

#### 4.4 State Management
- **Context** cho global state (auth, cart, settings)
- **Custom hooks** cho data fetching và business logic
- **Zustand** cho complex state khi cần thiết
- Avoid prop drilling bằng context composition

### 5. Testing Strategy
- **Unit tests** cho utilities và pure functions
- **Integration tests** cho APIs và repositories
- **Component tests** với React Testing Library
- **E2E tests** với Playwright cho critical flows

### 6. Database và Migration
- Prisma schema là single source of truth
- Migration files trong `prisma/migrations/`
- Seeders trong `prisma/seeders/` với factory pattern
- Always backup trước khi migrate production

### 7. Security và Performance
- **Admin authentication** tách biệt khỏi user auth
- **Audit logging** cho admin actions
- **Caching strategy** với appropriate TTL
- **Image optimization** với Next.js Image component
- **Bundle optimization** với dynamic imports

### 8. Environment và Deployment
- Development server: `http://localhost:6002`
- Docker support với `docker-compose.yml`
- Environment variables documented
- Build optimization với Turbopack

### 9. Code Quality Rules
- **TypeScript strict mode** enabled
- **ESLint** với custom rules
- **Prettier** cho code formatting
- **Husky** pre-commit hooks
- Always run `npx tsc` trước khi commit

### 10. Notification và Event System
- **Event-driven architecture** cho business events
- **Email service** với multiple providers (SendGrid, SES)
- **Notification system** với preferences
- **Audit trail** cho admin actions
